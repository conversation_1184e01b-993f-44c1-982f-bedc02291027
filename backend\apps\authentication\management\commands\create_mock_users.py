from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.authentication.models import User
from utils.crypto import SM3Hasher, SM4Crypto
import binascii
import random
import string
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '创建模拟用户数据'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=10, help='要创建的用户数量')
        parser.add_argument('--prefix', type=str, default='2023', help='学号前缀')
        parser.add_argument('--password', type=str, default='123456', help='默认密码')
        parser.add_argument('--email-domain', type=str, default='example.com', help='邮箱域名')
        parser.add_argument('--force', action='store_true', help='强制创建，即使学号已存在')

    def handle(self, *args, **options):
        count = options['count']
        prefix = options['prefix']
        default_password = options['password']
        email_domain = options['email_domain']
        force = options['force']
        
        self.stdout.write(f'开始创建 {count} 个模拟用户...')
        
        # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
        sm4_key = SM4Crypto.generate_key()
        
        created_count = 0
        skipped_count = 0
        
        for i in range(1, count + 1):
            # 生成11位学号，格式为前缀+随机数字
            suffix_length = 11 - len(prefix)
            if suffix_length <= 0:
                self.stdout.write(self.style.ERROR(f'前缀 "{prefix}" 过长，无法生成11位学号'))
                return
                
            suffix = ''.join(random.choices('0123456789', k=suffix_length))
            student_id = f"{prefix}{suffix}"
            
            # 计算学号的SM3哈希值用于检查是否已存在
            student_id_hash = SM3Hasher.hash(student_id)
            
            # 检查学号是否已被注册
            if User.objects.filter(student_id_hash=student_id_hash).exists():
                if force:
                    self.stdout.write(f'学号 {student_id} 已存在，但由于使用了--force参数，将跳过')
                    skipped_count += 1
                    continue
                else:
                    # 生成新的学号
                    for _ in range(10):  # 尝试10次
                        suffix = ''.join(random.choices('0123456789', k=suffix_length))
                        student_id = f"{prefix}{suffix}"
                        student_id_hash = SM3Hasher.hash(student_id)
                        if not User.objects.filter(student_id_hash=student_id_hash).exists():
                            break
                    else:
                        self.stdout.write(self.style.ERROR(f'无法为第 {i} 个用户生成唯一学号，跳过'))
                        skipped_count += 1
                        continue
            
            # 生成随机邮箱
            email = f"{student_id}@{email_domain}"
            
            # 生成随机手机号
            phone = f"1{random.choice(['3', '5', '7', '8', '9'])}{random.choices('0123456789', k=9)}"
            phone = ''.join(phone)
            
            try:
                # 加密学号
                encrypted_student_id = SM4Crypto.encrypt(sm4_key, student_id)
                student_id_binary = binascii.unhexlify(encrypted_student_id)
                
                # 使用SM3哈希密码
                password_hash_result = SM3Hasher.hash_with_salt(default_password)
                
                # 加密邮箱
                encrypted_email = SM4Crypto.encrypt(sm4_key, email)
                email_binary = binascii.unhexlify(encrypted_email)
                
                # 加密手机号
                encrypted_phone = SM4Crypto.encrypt(sm4_key, phone)
                phone_binary = binascii.unhexlify(encrypted_phone)
                
                # 创建用户对象
                user = User(
                    student_id=student_id_binary,
                    student_id_hash=student_id_hash,
                    password=password_hash_result['hash'],
                    salt=password_hash_result['salt'],
                    iterations=password_hash_result['iterations'],
                    email=email_binary,
                    phone=phone_binary,
                    status='active',
                    credit_score=100
                )
                user.save()
                
                created_count += 1
                self.stdout.write(f'创建用户 {i}/{count}: 学号={student_id}, 邮箱={email}, 手机号={phone}')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'创建用户 {student_id} 时出错: {str(e)}'))
                skipped_count += 1
        
        self.stdout.write(self.style.SUCCESS(f'成功创建 {created_count} 个用户，跳过 {skipped_count} 个'))
