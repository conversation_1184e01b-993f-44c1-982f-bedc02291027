"""
日志模块定时任务
"""
import logging
from datetime import timedelta
from django.utils import timezone
from celery import shared_task
from .models import SystemLog, UserActionLog, SecurityLog, ApiRequestLog, CeleryTask
from .utils import verify_log_chain, add_system_log, add_security_log

logger = logging.getLogger(__name__)

@shared_task
def verify_system_log_chain():
    """验证系统日志哈希链"""
    try:
        # 获取最近一天的日志
        yesterday = timezone.now() - timedelta(days=1)

        # 获取最早的日志ID
        first_log = SystemLog.objects.filter(created_at__gte=yesterday).order_by('id').first()
        if not first_log:
            logger.info("没有需要验证的系统日志")
            return {"status": "success", "message": "没有需要验证的系统日志"}

        # 验证日志链
        is_valid, problematic_ids = verify_log_chain('system', start_id=first_log.id)

        # 记录验证结果
        if is_valid:
            add_system_log(
                log_type='info',
                module='log',
                action='verify_chain',
                description=f"系统日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志"
            )
            logger.info(f"系统日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志")
        else:
            # 记录安全事件
            add_security_log(
                log_type='log_tampering',
                description=f"系统日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}",
                ip_address=None,
                user_agent=None,
                status='failed'
            )
            logger.error(f"系统日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}")

        return {
            "status": "success" if is_valid else "failed",
            "message": f"验证了从ID {first_log.id} 开始的系统日志",
            "is_valid": is_valid,
            "problematic_ids": problematic_ids
        }
    except Exception as e:
        logger.error(f"验证系统日志哈希链失败: {str(e)}")
        return {"status": "error", "message": str(e)}


@shared_task
def verify_user_action_log_chain():
    """验证用户操作日志哈希链"""
    try:
        # 获取最近一天的日志
        yesterday = timezone.now() - timedelta(days=1)

        # 获取最早的日志ID
        first_log = UserActionLog.objects.filter(created_at__gte=yesterday).order_by('id').first()
        if not first_log:
            logger.info("没有需要验证的用户操作日志")
            return {"status": "success", "message": "没有需要验证的用户操作日志"}

        # 验证日志链
        is_valid, problematic_ids = verify_log_chain('user_action', start_id=first_log.id)

        # 记录验证结果
        if is_valid:
            add_system_log(
                log_type='info',
                module='log',
                action='verify_chain',
                description=f"用户操作日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志"
            )
            logger.info(f"用户操作日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志")
        else:
            # 记录安全事件
            add_security_log(
                log_type='log_tampering',
                description=f"用户操作日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}",
                ip_address=None,
                user_agent=None,
                status='failed'
            )
            logger.error(f"用户操作日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}")

        return {
            "status": "success" if is_valid else "failed",
            "message": f"验证了从ID {first_log.id} 开始的用户操作日志",
            "is_valid": is_valid,
            "problematic_ids": problematic_ids
        }
    except Exception as e:
        logger.error(f"验证用户操作日志哈希链失败: {str(e)}")
        return {"status": "error", "message": str(e)}


@shared_task
def verify_security_log_chain():
    """验证安全日志哈希链"""
    try:
        # 获取最近一天的日志
        yesterday = timezone.now() - timedelta(days=1)

        # 获取最早的日志ID
        first_log = SecurityLog.objects.filter(created_at__gte=yesterday).order_by('id').first()
        if not first_log:
            logger.info("没有需要验证的安全日志")
            return {"status": "success", "message": "没有需要验证的安全日志"}

        # 验证日志链
        is_valid, problematic_ids = verify_log_chain('security', start_id=first_log.id)

        # 记录验证结果
        if is_valid:
            add_system_log(
                log_type='info',
                module='log',
                action='verify_chain',
                description=f"安全日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志"
            )
            logger.info(f"安全日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志")
        else:
            # 记录安全事件
            add_security_log(
                log_type='log_tampering',
                description=f"安全日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}",
                ip_address=None,
                user_agent=None,
                status='failed'
            )
            logger.error(f"安全日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}")

        return {
            "status": "success" if is_valid else "failed",
            "message": f"验证了从ID {first_log.id} 开始的安全日志",
            "is_valid": is_valid,
            "problematic_ids": problematic_ids
        }
    except Exception as e:
        logger.error(f"验证安全日志哈希链失败: {str(e)}")
        return {"status": "error", "message": str(e)}


@shared_task
def verify_api_request_log_chain():
    """验证API请求日志哈希链"""
    try:
        # 获取最近一天的日志
        yesterday = timezone.now() - timedelta(days=1)

        # 获取最早的日志ID
        first_log = ApiRequestLog.objects.filter(created_at__gte=yesterday).order_by('id').first()
        if not first_log:
            logger.info("没有需要验证的API请求日志")
            return {"status": "success", "message": "没有需要验证的API请求日志"}

        # 验证日志链
        is_valid, problematic_ids = verify_log_chain('api_request', start_id=first_log.id)

        # 记录验证结果
        if is_valid:
            add_system_log(
                log_type='info',
                module='log',
                action='verify_chain',
                description=f"API请求日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志"
            )
            logger.info(f"API请求日志哈希链验证成功，验证了从ID {first_log.id} 开始的日志")
        else:
            # 记录安全事件
            add_security_log(
                log_type='log_tampering',
                description=f"API请求日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}",
                ip_address=None,
                user_agent=None,
                status='failed'
            )
            logger.error(f"API请求日志哈希链验证失败，发现 {len(problematic_ids)} 条问题记录: {problematic_ids}")

        return {
            "status": "success" if is_valid else "failed",
            "message": f"验证了从ID {first_log.id} 开始的API请求日志",
            "is_valid": is_valid,
            "problematic_ids": problematic_ids
        }
    except Exception as e:
        logger.error(f"验证API请求日志哈希链失败: {str(e)}")
        return {"status": "error", "message": str(e)}


@shared_task
def clean_old_logs():
    """清理旧日志"""
    try:
        # 保留最近90天的日志
        retention_days = 90
        cutoff_date = timezone.now() - timedelta(days=retention_days)

        # 清理各类日志
        system_count = SystemLog.objects.filter(created_at__lt=cutoff_date).count()
        SystemLog.objects.filter(created_at__lt=cutoff_date).delete()

        user_action_count = UserActionLog.objects.filter(created_at__lt=cutoff_date).count()
        UserActionLog.objects.filter(created_at__lt=cutoff_date).delete()

        security_count = SecurityLog.objects.filter(created_at__lt=cutoff_date).count()
        SecurityLog.objects.filter(created_at__lt=cutoff_date).delete()

        api_request_count = ApiRequestLog.objects.filter(created_at__lt=cutoff_date).count()
        ApiRequestLog.objects.filter(created_at__lt=cutoff_date).delete()

        # 清理Celery任务记录
        celery_task_count = CeleryTask.objects.filter(date_created__lt=cutoff_date).count()
        CeleryTask.objects.filter(date_created__lt=cutoff_date).delete()

        total_count = system_count + user_action_count + security_count + api_request_count + celery_task_count

        # 记录清理结果
        add_system_log(
            log_type='info',
            module='log',
            action='clean_logs',
            description=f"清理了 {total_count} 条旧日志，包括 {system_count} 条系统日志、{user_action_count} 条用户操作日志、{security_count} 条安全日志、{api_request_count} 条API请求日志、{celery_task_count} 条Celery任务记录"
        )

        logger.info(f"清理了 {total_count} 条旧日志")

        return {
            "status": "success",
            "message": f"清理了 {total_count} 条旧日志",
            "details": {
                "system_count": system_count,
                "user_action_count": user_action_count,
                "security_count": security_count,
                "api_request_count": api_request_count,
                "celery_task_count": celery_task_count
            }
        }
    except Exception as e:
        logger.error(f"清理旧日志失败: {str(e)}")
        return {"status": "error", "message": str(e)}
