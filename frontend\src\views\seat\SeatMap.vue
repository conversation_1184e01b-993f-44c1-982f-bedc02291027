<template>
  <div class="seat-map">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
        <h2 v-if="room">{{ room.name }} - 座位图</h2>
      </div>

      <div class="header-right">
        <el-select
          v-model="selectedDate"
          placeholder="选择日期"
          @change="loadSeats"
        >
          <el-option
            v-for="date in dateOptions"
            :key="date.value"
            :label="date.label"
            :value="date.value"
          />
        </el-select>

        <el-button type="primary" @click="loadSeats">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <template v-else>
      <el-card class="room-info-card" shadow="never">
        <div class="room-info">
          <div class="info-item">
            <el-icon><Location /></el-icon>
            <span>位置: {{ room?.location }}</span>
          </div>

          <div class="info-item">
            <el-icon><Clock /></el-icon>
            <span>
              开放时间: {{ formatTime(room?.open_time) }} -
              {{ formatTime(room?.close_time) }}
            </span>
          </div>

          <div class="info-item">
            <el-icon><User /></el-icon>
            <span>容量: {{ room?.capacity }}座</span>
          </div>

          <div class="info-item">
            <el-icon><InfoFilled /></el-icon>
            <span>可用座位: {{ availableSeats }}/{{ room?.capacity }}</span>
          </div>
        </div>

        <div class="seat-legend">
          <div class="legend-item">
            <div class="seat-icon available"></div>
            <span>可用</span>
          </div>

          <div class="legend-item">
            <div class="seat-icon occupied"></div>
            <span>已占用</span>
          </div>

          <div class="legend-item">
            <div class="seat-icon disabled"></div>
            <span>禁用</span>
          </div>

          <div class="legend-item">
            <div class="seat-icon power-outlet"></div>
            <span>电源</span>
          </div>

          <div class="legend-item">
            <div class="seat-icon window"></div>
            <span>靠窗</span>
          </div>
        </div>
      </el-card>

      <div class="map-container">
        <div class="seat-filter">
          <el-checkbox v-model="filters.powerOutlet" @change="applyFilters">
            只看有电源的座位
          </el-checkbox>

          <el-checkbox v-model="filters.windowSeat" @change="applyFilters">
            只看靠窗座位
          </el-checkbox>

          <el-checkbox v-model="filters.availableOnly" @change="applyFilters">
            只看可用座位
          </el-checkbox>
        </div>

        <div class="seat-grid" :style="gridStyle">
          <div
            v-for="seat in filteredSeats"
            :key="seat.id"
            class="seat"
            :class="getSeatClasses(seat)"
            :style="getSeatStyle(seat)"
            @click="selectSeat(seat)"
          >
            <div class="seat-number">{{ seat.seat_number }}</div>
            <div class="seat-icons">
              <el-icon v-if="seat.is_power_outlet" class="power-icon"
                ><Lightning
              /></el-icon>
              <el-icon v-if="seat.is_window_seat" class="window-icon"
                ><Sunny
              /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 座位详情对话框 -->
      <el-dialog
        v-model="seatDialogVisible"
        :title="`座位详情 - ${selectedSeat?.seat_number}`"
        width="500px"
      >
        <div v-if="selectedSeat" class="seat-detail">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="座位编号">
              {{ selectedSeat.seat_number }}
            </el-descriptions-item>
            <el-descriptions-item label="位置">
              {{ `${selectedSeat.row}排${selectedSeat.column}列` }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getSeatStatusType(selectedSeat.status)">
                {{ getSeatStatusText(selectedSeat.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="设施">
              <el-tag
                v-if="selectedSeat.is_power_outlet"
                type="success"
                effect="plain"
              >
                有电源
              </el-tag>
              <el-tag
                v-if="selectedSeat.is_window_seat"
                type="success"
                effect="plain"
                >靠窗</el-tag
              >
              <span
                v-if="
                  !selectedSeat.is_power_outlet && !selectedSeat.is_window_seat
                "
              >
                无特殊设施
              </span>
            </el-descriptions-item>
          </el-descriptions>

          <div
            v-if="selectedSeat.current_reservation"
            class="current-reservation"
          >
            <h4>当前预约信息</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="预约状态">
                <el-tag
                  :type="
                    getReservationStatusType(
                      selectedSeat.current_reservation.status
                    )
                  "
                >
                  {{
                    getReservationStatusText(
                      selectedSeat.current_reservation.status
                    )
                  }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="开始时间">
                {{
                  formatDateTime(selectedSeat.current_reservation.start_time)
                }}
              </el-descriptions-item>
              <el-descriptions-item label="结束时间">
                {{ formatDateTime(selectedSeat.current_reservation.end_time) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-if="selectedSeat.status === 'available'" class="seat-actions">
            <el-button type="primary" @click="reserveSeat"
              >预约此座位</el-button
            >
          </div>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  ArrowLeft,
  Refresh,
  Location,
  Clock,
  User,
  InfoFilled,
  Lightning,
  Sunny,
} from "@element-plus/icons-vue";
// 导入模拟数据
import { rooms as mockRooms, generateSeats } from "@/mock/data";

export default {
  name: "SeatMap",
  setup() {
    const route = useRoute();
    const router = useRouter();

    const loading = ref(true);
    const room = ref(null);
    const seats = ref([]);
    const selectedDate = ref(formatDateForSelect(new Date()));
    const seatDialogVisible = ref(false);
    const selectedSeat = ref(null);

    // 过滤条件
    const filters = reactive({
      powerOutlet: false,
      windowSeat: false,
      availableOnly: false,
    });

    // 日期选项
    const dateOptions = computed(() => {
      const options = [];
      const today = new Date();

      for (let i = 0; i < 7; i++) {
        const date = new Date();
        date.setDate(today.getDate() + i);

        options.push({
          value: formatDateForSelect(date),
          label: formatDateForDisplay(date),
        });
      }

      return options;
    });

    // 可用座位数量
    const availableSeats = computed(() => {
      return seats.value.filter((seat) => seat.status === "available").length;
    });

    // 过滤后的座位
    const filteredSeats = computed(() => {
      return seats.value.filter((seat) => {
        if (filters.powerOutlet && !seat.is_power_outlet) return false;
        if (filters.windowSeat && !seat.is_window_seat) return false;
        if (filters.availableOnly && seat.status !== "available") return false;
        return true;
      });
    });

    // 座位网格样式
    const gridStyle = computed(() => {
      if (!room.value) return {};

      // 固定6x6网格
      return {
        gridTemplateRows: `repeat(6, 60px)`,
        gridTemplateColumns: `repeat(6, 60px)`,
      };
    });

    // 加载自习室和座位信息
    const loadRoomAndSeats = async () => {
      try {
        loading.value = true;

        const roomId = parseInt(route.query.roomId);
        if (!roomId) {
          ElMessage.error("缺少自习室ID参数");
          router.push("/seat/rooms");
          return;
        }

        // 从模拟数据中查找自习室
        const roomData = mockRooms.find((r) => r.id === roomId);
        if (!roomData) {
          ElMessage.error("未找到自习室信息");
          router.push("/seat/rooms");
          return;
        }

        // 转换数据格式
        room.value = {
          ...roomData,
          open_time: roomData.openTime,
          close_time: roomData.closeTime,
          available_seats: roomData.availableSeats,
        };

        // 加载座位信息
        await loadSeats();
      } catch (error) {
        ElMessage.error("加载自习室信息失败");
        router.push("/seat/rooms");
      } finally {
        loading.value = false;
      }
    };

    // 加载座位信息
    const loadSeats = async () => {
      try {
        loading.value = true;

        const roomId = parseInt(route.query.roomId);
        if (!roomId) return;

        // 模拟API请求延迟
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 使用模拟数据生成座位
        const seatsData = generateSeats(roomId);

        // 转换数据格式
        seats.value = seatsData.map((seat) => ({
          ...seat,
          is_power_outlet: seat.isPowerOutlet,
          is_window_seat: seat.isWindowSeat,
          seat_number: seat.seatNumber,
        }));
      } catch (error) {
        ElMessage.error("加载座位信息失败");
      } finally {
        loading.value = false;
      }
    };

    // 应用过滤器
    const applyFilters = () => {
      // 过滤逻辑已在计算属性中实现
    };

    // 选择座位
    const selectSeat = (seat) => {
      selectedSeat.value = seat;
      seatDialogVisible.value = true;
    };

    // 预约座位
    const reserveSeat = () => {
      if (!selectedSeat.value) return;

      router.push({
        path: "/seat/reservation",
        query: {
          seatId: selectedSeat.value.id,
          date: selectedDate.value,
        },
      });
    };

    // 获取座位类名
    const getSeatClasses = (seat) => {
      return {
        "seat-available": seat.status === "available",
        "seat-occupied": seat.status === "occupied",
        "seat-disabled": seat.status === "disabled",
        "seat-power": seat.is_power_outlet,
        "seat-window": seat.is_window_seat,
      };
    };

    // 获取座位样式
    const getSeatStyle = (seat) => {
      return {
        gridRow: `${seat.row} / span 1`,
        gridColumn: `${seat.column} / span 1`,
      };
    };

    // 获取座位状态类型
    const getSeatStatusType = (status) => {
      switch (status) {
        case "available":
          return "success";
        case "occupied":
          return "danger";
        case "disabled":
          return "info";
        default:
          return "info";
      }
    };

    // 获取座位状态文本
    const getSeatStatusText = (status) => {
      switch (status) {
        case "available":
          return "可用";
        case "occupied":
          return "已占用";
        case "disabled":
          return "禁用";
        default:
          return "未知状态";
      }
    };

    // 获取预约状态类型
    const getReservationStatusType = (status) => {
      switch (status) {
        case "pending":
          return "warning";
        case "checked_in":
          return "success";
        case "completed":
          return "info";
        case "cancelled":
          return "danger";
        case "timeout":
          return "danger";
        default:
          return "info";
      }
    };

    // 获取预约状态文本
    const getReservationStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待签到";
        case "checked_in":
          return "已签到";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        case "timeout":
          return "已超时";
        default:
          return "未知状态";
      }
    };

    // 格式化时间
    const formatTime = (timeString) => {
      if (!timeString) return "";

      // 时间格式为 "HH:MM:SS"，只显示 "HH:MM"
      return timeString.substring(0, 5);
    };

    // 格式化日期时间
    const formatDateTime = (dateTimeString) => {
      if (!dateTimeString) return "";

      const date = new Date(dateTimeString);
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
        date.getDate()
      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
    };

    // 格式化日期（用于选择器值）
    function formatDateForSelect(date) {
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
        date.getDate()
      )}`;
    }

    // 格式化日期（用于显示）
    function formatDateForDisplay(date) {
      const today = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(today.getDate() + 1);

      if (date.toDateString() === today.toDateString()) {
        return "今天";
      } else if (date.toDateString() === tomorrow.toDateString()) {
        return "明天";
      } else {
        const weekdays = [
          "周日",
          "周一",
          "周二",
          "周三",
          "周四",
          "周五",
          "周六",
        ];
        return `${date.getMonth() + 1}月${date.getDate()}日 ${
          weekdays[date.getDay()]
        }`;
      }
    }

    // 补零
    function padZero(num) {
      return num < 10 ? `0${num}` : num;
    }

    onMounted(() => {
      loadRoomAndSeats();
    });

    return {
      loading,
      room,
      seats,
      selectedDate,
      dateOptions,
      filters,
      seatDialogVisible,
      selectedSeat,
      availableSeats,
      filteredSeats,
      gridStyle,
      loadSeats,
      applyFilters,
      selectSeat,
      reserveSeat,
      getSeatClasses,
      getSeatStyle,
      getSeatStatusType,
      getSeatStatusText,
      getReservationStatusType,
      getReservationStatusText,
      formatTime,
      formatDateTime,
      ArrowLeft,
      Refresh,
      Location,
      Clock,
      User,
      InfoFilled,
      Lightning,
      Sunny,
    };
  },
};
</script>

<style lang="scss" scoped>
.seat-map {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    h2 {
      margin: 0;
    }
  }

  .header-right {
    display: flex;
    gap: 10px;
  }
}

.loading-container {
  padding: 40px 0;
}

.room-info-card {
  margin-bottom: 20px;

  .room-info {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;

    .info-item {
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 8px;
        color: #909399;
      }
    }
  }

  .seat-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    .legend-item {
      display: flex;
      align-items: center;

      .seat-icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        margin-right: 5px;

        &.available {
          background-color: #67c23a;
        }

        &.occupied {
          background-color: #f56c6c;
        }

        &.disabled {
          background-color: #909399;
        }

        &.power-outlet {
          background-color: #fff;
          border: 1px solid #67c23a;
          position: relative;

          &::after {
            content: "⚡";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
          }
        }

        &.window {
          background-color: #fff;
          border: 1px solid #409eff;
          position: relative;

          &::after {
            content: "☀";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
          }
        }
      }
    }
  }
}

.map-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .seat-filter {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }

  .seat-grid {
    display: grid;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;

    .seat {
      width: 50px;
      height: 50px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: transform 0.2s;
      position: relative;

      &:hover {
        transform: scale(1.1);
        z-index: 1;
      }

      &.seat-available {
        background-color: #67c23a;
        color: #fff;
      }

      &.seat-occupied {
        background-color: #f56c6c;
        color: #fff;
      }

      &.seat-disabled {
        background-color: #909399;
        color: #fff;
        cursor: not-allowed;

        &:hover {
          transform: none;
        }
      }

      .seat-number {
        font-weight: bold;
        font-size: 14px;
      }

      .seat-icons {
        display: flex;
        gap: 2px;
        margin-top: 2px;

        .power-icon,
        .window-icon {
          font-size: 12px;
        }
      }
    }
  }
}

.seat-detail {
  .current-reservation {
    margin-top: 20px;
  }

  .seat-actions {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
