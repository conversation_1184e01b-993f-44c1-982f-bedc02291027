from django.shortcuts import render
from django.utils import timezone
from rest_framework import status, viewsets, mixins, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from .models import Admin, AdminSession, SystemConfig, AdminOperationLog
from .serializers import (
    AdminLoginSerializer, AdminSerializer, AdminDetailSerializer,
    AdminUpdateSerializer, SystemConfigSerializer, AdminOperationLogSerializer
)
from utils.crypto import SM3Hasher, SM4Crypto
import binascii
import secrets
import logging

logger = logging.getLogger(__name__)

class IsAdminUser(permissions.BasePermission):
    """
    自定义权限：只允许管理员访问
    """
    def has_permission(self, request, view):
        return isinstance(request.user, Admin)


class AdminLoginView(APIView):
    """管理员登录视图"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = AdminLoginSerializer(data=request.data)
        if serializer.is_valid():
            admin = serializer.validated_data['admin']

            # 生成JWT令牌
            refresh = RefreshToken.for_user(admin)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            # 记录会话信息
            admin_session = AdminSession.objects.create(
                admin=admin,
                token=access_token,
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                expires_at=timezone.now() + timezone.timedelta(hours=1)
            )

            # 更新管理员最后登录时间
            admin.last_login = timezone.now()
            admin.save()

            # 记录操作日志
            AdminOperationLog.objects.create(
                admin=admin,
                operation='login',
                description='管理员登录',
                ip_address=self.get_client_ip(request),
                status='success'
            )

            return Response({
                'access': access_token,
                'refresh': refresh_token,
                'admin_id': admin.id,
                'username': admin.username,
                'role': admin.role
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AdminLogoutView(APIView):
    """管理员登出视图"""
    permission_classes = [IsAdminUser]

    def post(self, request):
        # 获取当前管理员的会话
        try:
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if ' ' in auth_header:
                token = auth_header.split(' ')[1]
                admin_session = AdminSession.objects.get(token=token, admin=request.user)
                admin_session.is_active = False
                admin_session.save()

                # 记录操作日志
                AdminOperationLog.objects.create(
                    admin=request.user,
                    operation='logout',
                    description='管理员登出',
                    ip_address=self.get_client_ip(request),
                    status='success'
                )
        except AdminSession.DoesNotExist:
            pass

        return Response({'message': '登出成功'})

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AdminViewSet(viewsets.GenericViewSet,
                   mixins.RetrieveModelMixin,
                   mixins.UpdateModelMixin,
                   mixins.ListModelMixin):
    """管理员视图集"""
    queryset = Admin.objects.all()
    permission_classes = [IsAdminUser]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return AdminDetailSerializer
        elif self.action == 'update' or self.action == 'partial_update':
            return AdminUpdateSerializer
        return AdminSerializer

    def get_object(self):
        if self.kwargs.get('pk') == 'me':
            return self.request.user
        return super().get_object()

    @action(detail=False, methods=['get'])
    def me(self, request):
        """获取当前管理员信息"""
        serializer = AdminDetailSerializer(request.user)
        return Response(serializer.data)

    def perform_update(self, serializer):
        admin = serializer.save()

        # 记录操作日志
        AdminOperationLog.objects.create(
            admin=self.request.user,
            operation='update_admin',
            description=f'更新管理员信息: {admin.username}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            target_type='admin',
            target_id=admin.id,
            status='success'
        )


class SystemConfigViewSet(viewsets.ModelViewSet):
    """系统配置视图集"""
    queryset = SystemConfig.objects.all()
    serializer_class = SystemConfigSerializer
    permission_classes = [IsAdminUser]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def perform_create(self, serializer):
        config = serializer.save()

        # 记录操作日志
        AdminOperationLog.objects.create(
            admin=self.request.user,
            operation='create_config',
            description=f'创建系统配置: {config.key}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            target_type='system_config',
            target_id=config.id,
            status='success'
        )

    def perform_update(self, serializer):
        config = serializer.save()

        # 记录操作日志
        AdminOperationLog.objects.create(
            admin=self.request.user,
            operation='update_config',
            description=f'更新系统配置: {config.key}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            target_type='system_config',
            target_id=config.id,
            status='success'
        )

    def perform_destroy(self, instance):
        key = instance.key
        instance_id = instance.id
        instance.delete()

        # 记录操作日志
        AdminOperationLog.objects.create(
            admin=self.request.user,
            operation='delete_config',
            description=f'删除系统配置: {key}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            target_type='system_config',
            target_id=instance_id,
            status='success'
        )


class AdminOperationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """管理员操作日志视图集"""
    serializer_class = AdminOperationLogSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        queryset = AdminOperationLog.objects.all().order_by('-created_at')

        # 过滤参数
        admin_id = self.request.query_params.get('admin_id')
        if admin_id:
            queryset = queryset.filter(admin_id=admin_id)

        operation = self.request.query_params.get('operation')
        if operation:
            queryset = queryset.filter(operation=operation)

        status_param = self.request.query_params.get('status')
        if status_param:
            queryset = queryset.filter(status=status_param)

        return queryset
