from rest_framework import serializers
from .models import SystemLog, UserActionLog, SecurityLog, ApiRequestLog
from apps.authentication.models import User

class SystemLogSerializer(serializers.ModelSerializer):
    """系统日志序列化器"""
    class Meta:
        model = SystemLog
        fields = ['id', 'log_type', 'module', 'action', 'description', 
                  'ip_address', 'user_id', 'user_type', 'created_at', 
                  'hash_value', 'prev_hash']
        read_only_fields = fields


class UserActionLogSerializer(serializers.ModelSerializer):
    """用户操作日志序列化器"""
    user_student_id_hash = serializers.CharField(source='user.student_id_hash', read_only=True)
    
    class Meta:
        model = UserActionLog
        fields = ['id', 'user', 'user_student_id_hash', 'action', 'description', 
                  'ip_address', 'user_agent', 'target_type', 'target_id', 
                  'status', 'created_at', 'hash_value', 'prev_hash']
        read_only_fields = fields


class SecurityLogSerializer(serializers.ModelSerializer):
    """安全日志序列化器"""
    class Meta:
        model = SecurityLog
        fields = ['id', 'log_type', 'user_id', 'user_type', 'ip_address', 
                  'user_agent', 'description', 'status', 'created_at', 
                  'hash_value', 'prev_hash']
        read_only_fields = fields


class ApiRequestLogSerializer(serializers.ModelSerializer):
    """API请求日志序列化器"""
    class Meta:
        model = ApiRequestLog
        fields = ['id', 'request_id', 'user_id', 'user_type', 'method', 
                  'path', 'query_params', 'response_status', 'response_time', 
                  'ip_address', 'user_agent', 'created_at', 'hash_value', 'prev_hash']
        read_only_fields = fields


class LogVerificationSerializer(serializers.Serializer):
    """日志验证序列化器"""
    log_type = serializers.ChoiceField(choices=['system', 'user_action', 'security', 'api_request'], required=True)
    log_id = serializers.IntegerField(required=True)
    
    def validate(self, attrs):
        log_type = attrs.get('log_type')
        log_id = attrs.get('log_id')
        
        # 根据日志类型获取对应的日志记录
        if log_type == 'system':
            try:
                log = SystemLog.objects.get(id=log_id)
            except SystemLog.DoesNotExist:
                raise serializers.ValidationError({'log_id': '系统日志不存在'})
        elif log_type == 'user_action':
            try:
                log = UserActionLog.objects.get(id=log_id)
            except UserActionLog.DoesNotExist:
                raise serializers.ValidationError({'log_id': '用户操作日志不存在'})
        elif log_type == 'security':
            try:
                log = SecurityLog.objects.get(id=log_id)
            except SecurityLog.DoesNotExist:
                raise serializers.ValidationError({'log_id': '安全日志不存在'})
        elif log_type == 'api_request':
            try:
                log = ApiRequestLog.objects.get(id=log_id)
            except ApiRequestLog.DoesNotExist:
                raise serializers.ValidationError({'log_id': 'API请求日志不存在'})
        
        # 验证哈希链
        if log.prev_hash:
            # 获取前一条日志记录
            if log_type == 'system':
                prev_log = SystemLog.objects.filter(id__lt=log.id).order_by('-id').first()
            elif log_type == 'user_action':
                prev_log = UserActionLog.objects.filter(id__lt=log.id).order_by('-id').first()
            elif log_type == 'security':
                prev_log = SecurityLog.objects.filter(id__lt=log.id).order_by('-id').first()
            elif log_type == 'api_request':
                prev_log = ApiRequestLog.objects.filter(id__lt=log.id).order_by('-id').first()
            
            if prev_log and prev_log.hash_value != log.prev_hash:
                attrs['is_valid'] = False
                attrs['error'] = '哈希链断裂，日志可能被篡改'
                return attrs
        
        attrs['is_valid'] = True
        attrs['log'] = log
        return attrs
