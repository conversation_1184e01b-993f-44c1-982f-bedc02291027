<template>
  <div class="app-sidebar">
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      :router="true"
      :collapse="isCollapse"
    >
      <el-menu-item index="/dashboard">
        <el-icon><Monitor /></el-icon>
        <template #title>首页</template>
      </el-menu-item>

      <el-sub-menu index="/seat">
        <template #title>
          <el-icon><SeatIcon /></el-icon>
          <span>座位管理</span>
        </template>
        <el-menu-item index="/seat/rooms">自习室列表</el-menu-item>
        <el-menu-item index="/seat/map">座位地图</el-menu-item>
        <el-menu-item index="/seat/reservation">预约座位</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="/user">
        <template #title>
          <el-icon><User /></el-icon>
          <span>个人中心</span>
        </template>
        <el-menu-item index="/user/profile">个人信息</el-menu-item>
        <el-menu-item index="/user/reservations">我的预约</el-menu-item>
        <el-menu-item index="/user/records">操作记录</el-menu-item>
        <el-menu-item index="/user/credit">信誉分记录</el-menu-item>
      </el-sub-menu>

      <el-menu-item index="/help">
        <el-icon><QuestionFilled /></el-icon>
        <template #title>帮助中心</template>
      </el-menu-item>
    </el-menu>

    <div class="sidebar-footer">
      <el-button
        type="text"
        :icon="isCollapse ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"
        @click="toggleCollapse"
      >
        {{ isCollapse ? "展开" : "收起" }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, computed, h } from "vue";
import { useRoute } from "vue-router";
import { Monitor, User, QuestionFilled } from "@element-plus/icons-vue";

// 自定义座位图标组件 - 使用Vue 3的函数式组件
const SeatIcon = {
  name: "SeatIcon",
  setup() {
    return () =>
      h(
        "svg",
        {
          viewBox: "0 0 24 24",
          width: "1em",
          height: "1em",
        },
        [
          h("path", {
            fill: "currentColor",
            d: "M4 18v-3h16v3h2v-6H2v6h2zm10-9h4V6h-4v3zm-6 0h4V6H8v3zM4 5v3h2V5h12v3h2V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2z",
          }),
        ]
      );
  },
};

export default {
  name: "AppSidebar",
  components: {
    Monitor,
    User,
    QuestionFilled,
    SeatIcon,
  },
  setup() {
    const route = useRoute();
    const isCollapse = ref(false);

    const activeMenu = computed(() => {
      const { path } = route;
      return path;
    });

    const toggleCollapse = () => {
      isCollapse.value = !isCollapse.value;
    };

    return {
      activeMenu,
      isCollapse,
      toggleCollapse,
    };
  },
};
</script>

<style lang="scss" scoped>
.app-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-right: 1px solid #e6e6e6;
  background-color: #fff;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
}

.sidebar-footer {
  padding: 10px;
  border-top: 1px solid #e6e6e6;
  text-align: center;
}
</style>
