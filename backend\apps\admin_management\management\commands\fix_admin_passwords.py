from django.core.management.base import BaseCommand
from apps.admin_management.models import Admin
from utils.crypto import SM3Hasher
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '修复管理员密码存储问题，将auth_key字段中的密码移动到正确的字段中'

    def handle(self, *args, **options):
        # 查找所有auth_key不为空但password为空或salt为空的管理员
        admins_to_fix = Admin.objects.filter(auth_key__isnull=False).exclude(auth_key='')
        
        fixed_count = 0
        for admin in admins_to_fix:
            if not admin.password or not admin.salt:
                # 如果密码或盐值为空，但auth_key不为空，说明密码可能被错误地存储在auth_key中
                # 使用auth_key作为明文密码，生成正确的哈希值和盐值
                password = admin.auth_key
                password_hash_result = SM3Hasher.hash_with_salt(password)
                
                admin.password = password_hash_result['hash']
                admin.salt = password_hash_result['salt']
                admin.iterations = password_hash_result['iterations']
                admin.auth_key = None  # 清空auth_key字段
                admin.save()
                
                fixed_count += 1
                self.stdout.write(self.style.SUCCESS(f'已修复管理员 {admin.username} 的密码存储'))
            else:
                # 如果密码和盐值都不为空，但auth_key也不为空，只需清空auth_key
                admin.auth_key = None
                admin.save()
                
                self.stdout.write(self.style.SUCCESS(f'已清空管理员 {admin.username} 的auth_key字段'))
                fixed_count += 1
        
        if fixed_count == 0:
            self.stdout.write(self.style.SUCCESS('没有需要修复的管理员密码'))
        else:
            self.stdout.write(self.style.SUCCESS(f'成功修复了 {fixed_count} 个管理员的密码存储'))
