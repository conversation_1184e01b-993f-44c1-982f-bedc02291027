"""
管理员认证后端
"""
import logging
from django.contrib.auth.backends import BaseBackend
from .models import Admin
from utils.crypto import SM3Hasher

logger = logging.getLogger(__name__)

class AdminBackend(BaseBackend):
    """
    管理员认证后端，用于Django admin登录验证
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        验证管理员凭据

        参数:
            request: 请求对象
            username: 用户名
            password: 密码

        返回:
            验证成功返回管理员对象，否则返回None
        """
        if username is None or password is None:
            return None

        try:
            admin = Admin.objects.get(username=username)

            # 检查管理员状态
            if admin.status != 'active':
                logger.warning(f"管理员 {username} 状态异常: {admin.status}")
                return None

            # 验证密码
            if not SM3Hasher.verify(password, admin.password, admin.salt, admin.iterations):
                logger.warning(f"管理员 {username} 密码验证失败")
                return None

            # 返回管理员对象
            logger.info(f"管理员 {username} 认证成功")
            return admin
        except Admin.DoesNotExist:
            logger.warning(f"尝试使用不存在的管理员用户名登录: {username}")
            return None
        except Exception as e:
            logger.error(f"管理员认证过程发生错误: {str(e)}")
            return None

    def get_user(self, user_id):
        """
        根据用户ID获取管理员对象

        参数:
            user_id: 管理员ID

        返回:
            管理员对象，如果不存在则返回None
        """
        try:
            admin = Admin.objects.get(pk=user_id)
            # 确保管理员状态为active
            if admin.status != 'active':
                return None
            # 设置is_superuser属性
            if admin.role == 'super_admin':
                admin.is_superuser = True
            return admin
        except Admin.DoesNotExist:
            return None

    def has_perm(self, user_obj, perm, obj=None):
        """
        检查用户是否有指定权限

        参数:
            user_obj: 用户对象
            perm: 权限字符串
            obj: 权限对象

        返回:
            是否有权限
        """
        if not user_obj.is_authenticated:
            return False

        return user_obj.has_perm(perm, obj)

    def has_module_perms(self, user_obj, app_label):
        """
        检查用户是否有访问指定应用的权限

        参数:
            user_obj: 用户对象
            app_label: 应用标签

        返回:
            是否有权限
        """
        if not user_obj.is_authenticated:
            return False

        return user_obj.has_module_perms(app_label)
