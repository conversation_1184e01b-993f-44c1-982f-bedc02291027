/**
 * 表单验证工具类
 */

/**
 * 验证是否为空
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  return value === undefined || value === null || value === "";
}

/**
 * 验证是否为非空
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为非空
 */
export function isNotEmpty(value) {
  return !isEmpty(value);
}

/**
 * 验证是否为数字
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为数字
 */
export function isNumber(value) {
  if (isEmpty(value)) return false;
  return !isNaN(Number(value));
}

/**
 * 验证是否为整数
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为整数
 */
export function isInteger(value) {
  if (!isNumber(value)) return false;
  return Number.isInteger(Number(value));
}

/**
 * 验证是否为正数
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为正数
 */
export function isPositive(value) {
  if (!isNumber(value)) return false;
  return Number(value) > 0;
}

/**
 * 验证是否为非负数
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为非负数
 */
export function isNonNegative(value) {
  if (!isNumber(value)) return false;
  return Number(value) >= 0;
}

/**
 * 验证是否为邮箱
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为邮箱
 */
export function isEmail(value) {
  if (isEmpty(value)) return false;
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(value);
}

/**
 * 验证是否为手机号
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为手机号
 */
export function isPhone(value) {
  if (isEmpty(value)) return false;
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(value);
}

/**
 * 验证是否为URL
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为URL
 */
export function isUrl(value) {
  if (isEmpty(value)) return false;
  try {
    new URL(value);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 验证是否为IP地址
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为IP地址
 */
export function isIp(value) {
  if (isEmpty(value)) return false;
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipRegex.test(value)) return false;

  const parts = value.split(".");
  return parts.every((part) => parseInt(part, 10) <= 255);
}

/**
 * 验证是否为身份证号
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为身份证号
 */
export function isIdCard(value) {
  if (isEmpty(value)) return false;
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(value);
}

/**
 * 验证是否为日期
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为日期
 */
export function isDate(value) {
  if (isEmpty(value)) return false;
  const date = new Date(value);
  return !isNaN(date.getTime());
}

/**
 * 验证字符串长度是否在指定范围内
 * @param {string} value - 待验证的值
 * @param {number} min - 最小长度
 * @param {number} max - 最大长度
 * @returns {boolean} 是否在指定范围内
 */
export function isLengthBetween(value, min, max) {
  if (isEmpty(value)) return false;
  const length = String(value).length;
  return length >= min && length <= max;
}

/**
 * 验证是否为密码（包含字母和数字，长度在6-20之间）
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为密码
 */
export function isPassword(value) {
  if (isEmpty(value)) return false;
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/;
  return passwordRegex.test(value);
}

/**
 * 验证是否为强密码（包含大小写字母、数字和特殊字符，长度在8-20之间）
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为强密码
 */
export function isStrongPassword(value) {
  if (isEmpty(value)) return false;
  const strongPasswordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/;
  return strongPasswordRegex.test(value);
}

/**
 * 验证是否为学号（11位数字）
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为学号
 */
export function isStudentId(value) {
  if (isEmpty(value)) return false;
  const studentIdRegex = /^\d{11}$/;
  return studentIdRegex.test(value);
}

/**
 * 验证是否为十六进制字符串
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为十六进制字符串
 */
export function isHex(value) {
  if (isEmpty(value)) return false;
  const hexRegex = /^[0-9a-fA-F]+$/;
  return hexRegex.test(value);
}

/**
 * 验证是否为Base64字符串
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否为Base64字符串
 */
export function isBase64(value) {
  if (isEmpty(value)) return false;
  const base64Regex = /^[A-Za-z0-9+/]+={0,2}$/;
  return base64Regex.test(value);
}

/**
 * 创建Element Plus表单验证规则
 */
export const rules = {
  /**
   * 必填项验证规则
   * @param {string} message - 错误提示信息
   * @returns {Object} 验证规则对象
   */
  required(message = "该字段不能为空") {
    return { required: true, message, trigger: "blur" };
  },

  /**
   * 邮箱验证规则
   * @param {string} message - 错误提示信息
   * @returns {Object} 验证规则对象
   */
  email(message = "请输入正确的邮箱格式") {
    return { type: "email", message, trigger: "blur" };
  },

  /**
   * 手机号验证规则
   * @param {string} message - 错误提示信息
   * @returns {Object} 验证规则对象
   */
  phone(message = "请输入正确的手机号格式") {
    return { pattern: /^1[3-9]\d{9}$/, message, trigger: "blur" };
  },

  /**
   * 学号验证规则
   * @param {string} message - 错误提示信息
   * @returns {Object} 验证规则对象
   */
  studentId(message = "请输入11位数字的学号") {
    return { pattern: /^\d{11}$/, message, trigger: "blur" };
  },

  /**
   * 密码验证规则
   * @param {string} message - 错误提示信息
   * @returns {Object} 验证规则对象
   */
  password(message = "密码必须包含字母和数字，长度在6-20之间") {
    return {
      pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/,
      message,
      trigger: "blur",
    };
  },

  /**
   * 长度验证规则
   * @param {number} min - 最小长度
   * @param {number} max - 最大长度
   * @param {string} message - 错误提示信息
   * @returns {Object} 验证规则对象
   */
  length(min, max, message = `长度应在${min}-${max}个字符之间`) {
    return { min, max, message, trigger: "blur" };
  },
};
