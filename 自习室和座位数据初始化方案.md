# 自习室和座位数据初始化方案

## 1. 现状分析与需求

经过检查系统代码，发现目前系统中尚未创建自习室和座位数据。系统已经完成了以下模块的开发：

- 用户认证模块
- 座位管理模块的模型定义和API接口
- 前端界面的基本功能

但是缺少实际的自习室和座位数据，这会导致系统无法正常展示和使用座位预约功能。

### 1.1 自习室设计需求

根据最新需求，自习室设计应满足以下条件：

- 图书馆共八层，1-6层为开放区域，7-8层为不开放区域
- 每层分东区西区，共两个自习室
- 每个自习室座位为6×6的布局，共36个座位
- 每个自习室座位靠边每隔一位有电源插座
- 管理员可以对自习室进行管理和描述

## 2. 数据初始化方案

### 2.1 手动创建方式

可以通过Django管理员界面手动创建自习室和座位数据：

1. 访问`/django-admin/`登录管理员界面
2. 进入`apps.seat` > `Rooms`创建自习室
3. 进入`apps.seat` > `Seats`创建座位

**优点**：简单直观，适合少量数据
**缺点**：效率低，不适合大量座位数据的创建

### 2.2 管理命令方式

开发Django管理命令来批量创建自习室和座位数据：

```python
# apps/seat/management/commands/create_library_data.py
from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.seat.models import Room, Seat
import datetime

class Command(BaseCommand):
    help = '创建图书馆自习室和座位数据'

    def add_arguments(self, parser):
        parser.add_argument('--force', action='store_true', help='强制重新创建数据（会删除现有数据）')

    def handle(self, *args, **options):
        force = options['force']

        # 检查是否已存在数据
        existing_rooms = Room.objects.count()
        if existing_rooms > 0 and not force:
            self.stdout.write(self.style.WARNING(f'已存在 {existing_rooms} 个自习室，使用 --force 参数强制重新创建'))
            return

        # 如果强制重新创建，先删除现有数据
        if force:
            self.stdout.write('删除现有数据...')
            Room.objects.all().delete()

        # 创建自习室
        self.stdout.write('开始创建自习室...')
        rooms = []

        # 图书馆共八层，1-6层为开放区域，7-8层不开放
        # 每层分东区西区，共两个自习室
        for floor in range(1, 9):
            for area in ['东区', '西区']:
                # 7-8层不开放
                status = 'open' if floor <= 6 else 'closed'

                # 每个自习室座位为6×6的布局
                capacity = 36

                room_name = f'{floor}楼{area}自习室'
                room = Room.objects.create(
                    name=room_name,
                    location=f'图书馆{floor}楼{area}',
                    floor=floor,
                    capacity=capacity,
                    open_time=datetime.time(8, 0),  # 8:00 AM
                    close_time=datetime.time(22, 0),  # 10:00 PM
                    status=status,
                    description=f'图书馆{floor}楼{area}自习室，{"开放" if status == "open" else "不开放"}区域'
                )
                rooms.append(room)
                self.stdout.write(f'  - 创建自习室: {room.name} (状态: {status})')

        # 为每个自习室创建座位
        self.stdout.write('开始创建座位...')
        for room in rooms:
            # 创建一个6×6的座位网格
            for row in range(1, 7):
                for col in range(1, 7):
                    seat_number = f'{row}-{col}'

                    # 每个自习室座位靠边每隔一位有电源插座
                    # 靠边的座位：第1行、第6行、第1列、第6列
                    is_edge_seat = row == 1 or row == 6 or col == 1 or col == 6

                    # 每隔一位有电源插座
                    is_power_outlet = is_edge_seat and ((row + col) % 2 == 0)

                    # 靠窗的座位：第1行和第6行
                    is_window_seat = row == 1 or row == 6

                    # 创建座位
                    Seat.objects.create(
                        room=room,
                        seat_number=seat_number,
                        row=row,
                        column=col,
                        status='available' if room.status == 'open' else 'disabled',
                        is_power_outlet=is_power_outlet,
                        is_window_seat=is_window_seat
                    )

            self.stdout.write(f'  - 为 {room.name} 创建了 36 个座位')

        total_rooms = len(rooms)
        total_seats = total_rooms * 36
        self.stdout.write(self.style.SUCCESS(f'成功创建 {total_rooms} 个自习室和 {total_seats} 个座位'))
```

使用方法：
```bash
python manage.py create_library_data
# 强制重新创建数据
python manage.py create_library_data --force
```

**优点**：高效，可重复执行，适合开发和测试环境
**缺点**：需要编写代码，不够灵活

### 2.3 数据迁移方式

通过Django数据迁移自动创建初始数据：

```python
# apps/seat/migrations/0002_initial_library_data.py
from django.db import migrations
import datetime

def create_initial_data(apps, schema_editor):
    Room = apps.get_model('seat', 'Room')
    Seat = apps.get_model('seat', 'Seat')

    # 创建自习室
    rooms = []

    # 图书馆共八层，1-6层为开放区域，7-8层不开放
    # 每层分东区西区，共两个自习室
    for floor in range(1, 9):
        for area_idx, area in enumerate(['东区', '西区']):
            # 7-8层不开放
            status = 'open' if floor <= 6 else 'closed'

            # 每个自习室座位为6×6的布局
            capacity = 36

            room_name = f'{floor}楼{area}自习室'
            room = Room.objects.create(
                name=room_name,
                location=f'图书馆{floor}楼{area}',
                floor=floor,
                capacity=capacity,
                open_time=datetime.time(8, 0),  # 8:00 AM
                close_time=datetime.time(22, 0),  # 10:00 PM
                status=status,
                description=f'图书馆{floor}楼{area}自习室，{"开放" if status == "open" else "不开放"}区域'
            )
            rooms.append(room)

    # 为每个自习室创建座位
    for room in rooms:
        # 创建一个6×6的座位网格
        for row in range(1, 7):
            for col in range(1, 7):
                seat_number = f'{row}-{col}'

                # 每个自习室座位靠边每隔一位有电源插座
                # 靠边的座位：第1行、第6行、第1列、第6列
                is_edge_seat = row == 1 or row == 6 or col == 1 or col == 6

                # 每隔一位有电源插座
                is_power_outlet = is_edge_seat and ((row + col) % 2 == 0)

                # 靠窗的座位：第1行和第6行
                is_window_seat = row == 1 or row == 6

                # 创建座位
                Seat.objects.create(
                    room=room,
                    seat_number=seat_number,
                    row=row,
                    column=col,
                    status='available' if room.status == 'open' else 'disabled',
                    is_power_outlet=is_power_outlet,
                    is_window_seat=is_window_seat
                )

def remove_initial_data(apps, schema_editor):
    Room = apps.get_model('seat', 'Room')
    Room.objects.all().delete()

class Migration(migrations.Migration):
    dependencies = [
        ('seat', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_initial_data, remove_initial_data),
    ]
```

**优点**：自动执行，适合生产环境部署
**缺点**：修改后需要创建新的迁移文件

### 2.4 数据导入工具

开发数据导入工具，支持从Excel或CSV导入自习室和座位数据：

```python
# apps/seat/management/commands/import_seats.py
import csv
import pandas as pd
from django.core.management.base import BaseCommand
from apps.seat.models import Room, Seat

class Command(BaseCommand):
    help = '从CSV文件导入自习室和座位数据'

    def add_arguments(self, parser):
        parser.add_argument('--rooms', type=str, help='自习室CSV文件路径')
        parser.add_argument('--seats', type=str, help='座位CSV文件路径')

    def handle(self, *args, **options):
        rooms_file = options.get('rooms')
        seats_file = options.get('seats')

        if rooms_file:
            self.import_rooms(rooms_file)

        if seats_file:
            self.import_seats(seats_file)

    def import_rooms(self, file_path):
        self.stdout.write(f'从 {file_path} 导入自习室数据...')
        try:
            df = pd.read_csv(file_path)
            count = 0

            for _, row in df.iterrows():
                room, created = Room.objects.update_or_create(
                    name=row['name'],
                    defaults={
                        'location': row['location'],
                        'floor': row['floor'],
                        'capacity': row['capacity'],
                        'open_time': row['open_time'],
                        'close_time': row['close_time'],
                        'status': row['status'],
                        'description': row.get('description', '')
                    }
                )
                if created:
                    count += 1

            self.stdout.write(self.style.SUCCESS(f'成功导入 {count} 个自习室'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'导入自习室失败: {str(e)}'))

    def import_seats(self, file_path):
        self.stdout.write(f'从 {file_path} 导入座位数据...')
        try:
            df = pd.read_csv(file_path)
            count = 0

            for _, row in df.iterrows():
                try:
                    room = Room.objects.get(name=row['room_name'])
                    seat, created = Seat.objects.update_or_create(
                        room=room,
                        seat_number=row['seat_number'],
                        defaults={
                            'row': row['row'],
                            'column': row['column'],
                            'status': row['status'],
                            'is_power_outlet': row['is_power_outlet'],
                            'is_window_seat': row['is_window_seat']
                        }
                    )
                    if created:
                        count += 1
                except Room.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f'自习室不存在: {row["room_name"]}'))

            self.stdout.write(self.style.SUCCESS(f'成功导入 {count} 个座位'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'导入座位失败: {str(e)}'))
```

**优点**：灵活，适合从现有系统迁移数据
**缺点**：需要准备CSV文件，操作相对复杂

## 3. 前端座位地图展示优化

为了更好地展示座位地图，建议对前端`SeatMap.vue`组件进行以下优化：

1. **座位布局优化**：根据实际自习室布局调整座位网格
2. **座位状态颜色**：使用更直观的颜色区分不同状态的座位
3. **座位图例**：添加图例说明不同颜色和图标的含义
4. **缩放功能**：添加座位地图的缩放和平移功能
5. **自适应布局**：根据屏幕大小自动调整座位地图的显示

## 4. 建议实施方案

考虑到系统的当前状态和需求，建议采用以下实施方案：

1. **开发阶段**：使用管理命令方式（2.2）快速创建测试数据
2. **测试阶段**：使用数据导入工具（2.4）导入更真实的测试数据
3. **生产部署**：使用数据迁移方式（2.3）确保初始数据的一致性

### 4.1 实施步骤

1. 创建管理命令`create_initial_data`用于开发测试
2. 开发数据导入工具支持CSV/Excel导入
3. 准备生产环境的数据迁移文件
4. 优化前端座位地图展示

### 4.2 数据模板

为方便使用数据导入工具，提供以下CSV模板：

**自习室模板 (rooms.csv)**:
```
name,location,floor,capacity,open_time,close_time,status,description
1楼东区自习室,图书馆1楼东区,1,36,08:00:00,22:00:00,open,图书馆1楼东区自习室，开放区域
1楼西区自习室,图书馆1楼西区,1,36,08:00:00,22:00:00,open,图书馆1楼西区自习室，开放区域
2楼东区自习室,图书馆2楼东区,2,36,08:00:00,22:00:00,open,图书馆2楼东区自习室，开放区域
2楼西区自习室,图书馆2楼西区,2,36,08:00:00,22:00:00,open,图书馆2楼西区自习室，开放区域
...
7楼东区自习室,图书馆7楼东区,7,36,08:00:00,22:00:00,closed,图书馆7楼东区自习室，不开放区域
```

**座位模板 (seats.csv)**:
```
room_name,seat_number,row,column,status,is_power_outlet,is_window_seat
1楼东区自习室,1-1,1,1,available,True,True
1楼东区自习室,1-2,1,2,available,False,True
1楼东区自习室,1-3,1,3,available,True,True
1楼东区自习室,1-4,1,4,available,False,True
1楼东区自习室,1-5,1,5,available,True,True
1楼东区自习室,1-6,1,6,available,False,True
...
```

## 5. 管理员自习室管理功能

根据需求，管理员需要能够对自习室进行管理和描述。Django管理员界面已经提供了这些功能：

### 5.1 自习室管理功能

管理员可以通过Django管理界面(`/django-admin/`)对自习室进行以下管理操作：

1. **查看自习室列表**：查看所有自习室的基本信息
2. **添加新自习室**：创建新的自习室
3. **编辑自习室信息**：修改自习室的名称、位置、描述等信息
4. **修改自习室状态**：将自习室设置为开放(open)、关闭(closed)或维护(maintenance)状态
5. **调整开放时间**：修改自习室的开放时间和关闭时间

### 5.2 座位管理功能

管理员可以通过Django管理界面对座位进行以下管理操作：

1. **查看座位列表**：查看所有座位的基本信息
2. **添加新座位**：在特定自习室中创建新的座位
3. **编辑座位信息**：修改座位的编号、位置、特性等信息
4. **修改座位状态**：将座位设置为可用(available)、占用(occupied)或禁用(disabled)状态
5. **设置座位特性**：设置座位是否有电源插座、是否靠窗等特性

### 5.3 管理界面优化建议

为了提升管理员的使用体验，建议对Django管理界面进行以下优化：

1. **批量操作功能**：添加批量修改座位状态的功能
2. **座位地图可视化**：在管理界面中添加座位地图可视化功能
3. **自定义过滤器**：添加更多的过滤选项，如按电源插座、靠窗等特性过滤
4. **导入导出功能**：添加自习室和座位数据的导入导出功能
5. **使用统计报表**：添加自习室和座位使用情况的统计报表

## 6. 总结

通过实施上述数据初始化方案，可以快速创建符合需求的自习室和座位数据，使系统能够正常展示和使用座位预约功能。建议根据实际需求选择合适的初始化方式，并结合前端优化提升用户体验。

管理员可以通过Django管理界面对自习室和座位进行全面管理，包括添加、编辑、修改状态等操作。通过优化管理界面，可以进一步提升管理效率和用户体验。
