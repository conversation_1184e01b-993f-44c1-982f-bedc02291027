from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import User, UserSession, CreditRecord
import binascii
from utils.crypto import SM4Crypto

class UserAdmin(admin.ModelAdmin):
    list_display = ('id', 'student_id_hash', 'email_display', 'status', 'credit_score', 'is_staff', 'last_login')
    list_filter = ('status', 'credit_score', 'is_staff', 'is_superuser', 'is_active')
    search_fields = ('student_id_hash', 'id')
    ordering = ('-created_at',)
    readonly_fields = ('student_id_hash', 'student_id_display', 'email_display', 'phone_display', 'last_login', 'created_at')

    fieldsets = (
        (None, {'fields': ('student_id_hash', 'student_id_display', 'password')}),
        (_('个人信息'), {'fields': ('email_display', 'phone_display', 'public_key')}),
        (_('状态信息'), {'fields': ('status', 'credit_score', 'login_attempts')}),
        (_('权限'), {
            'fields': ('is_active', 'is_staff', 'is_superuser'),
        }),
        (_('重要日期'), {'fields': ('last_login', 'created_at', 'updated_at')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('student_id_hash', 'password1', 'password2', 'status', 'is_staff', 'is_superuser'),
        }),
    )

    def student_id_display(self, obj):
        """显示解密后的学号"""
        try:
            if obj.student_id:
                # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
                sm4_key = SM4Crypto.generate_key()
                encrypted_student_id = binascii.hexlify(obj.student_id).decode()
                return SM4Crypto.decrypt(sm4_key, encrypted_student_id)
            return None
        except Exception as e:
            return f"解密失败: {str(e)}"
    student_id_display.short_description = '学号'

    def email_display(self, obj):
        """显示解密后的邮箱"""
        try:
            if obj.email:
                # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
                sm4_key = SM4Crypto.generate_key()
                encrypted_email = binascii.hexlify(obj.email).decode()
                return SM4Crypto.decrypt(sm4_key, encrypted_email)
            return None
        except Exception as e:
            return f"解密失败: {str(e)}"
    email_display.short_description = '邮箱'

    def phone_display(self, obj):
        """显示解密后的手机号"""
        try:
            if obj.phone:
                # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
                sm4_key = SM4Crypto.generate_key()
                encrypted_phone = binascii.hexlify(obj.phone).decode()
                return SM4Crypto.decrypt(sm4_key, encrypted_phone)
            return None
        except Exception as e:
            return f"解密失败: {str(e)}"
    phone_display.short_description = '手机号'

    def has_add_permission(self, request):
        """只有超级用户可以添加用户"""
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        """只有超级用户可以删除用户"""
        return request.user.is_superuser


class UserSessionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'ip_address', 'is_active', 'created_at', 'expires_at')
    list_filter = ('is_active',)
    search_fields = ('user__student_id_hash', 'ip_address')
    ordering = ('-created_at',)
    readonly_fields = ('user', 'token', 'ip_address', 'user_agent', 'created_at', 'expires_at')

    def has_add_permission(self, request):
        """禁止手动添加会话"""
        return False

    def has_change_permission(self, request, obj=None):
        """只允许修改is_active字段"""
        return True


class CreditRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'delta', 'score_after', 'reason', 'operator_type', 'created_at')
    list_filter = ('operator_type', 'delta')
    search_fields = ('user__student_id_hash', 'reason')
    ordering = ('-created_at',)
    readonly_fields = ('user', 'delta', 'score_after', 'reason', 'operator_id', 'operator_type', 'related_entity', 'related_id', 'created_at')

    def has_add_permission(self, request):
        """禁止手动添加信誉记录"""
        return False

    def has_change_permission(self, request, obj=None):
        """禁止修改信誉记录"""
        return False


admin.site.register(User, UserAdmin)
admin.site.register(UserSession, UserSessionAdmin)
admin.site.register(CreditRecord, CreditRecordAdmin)
