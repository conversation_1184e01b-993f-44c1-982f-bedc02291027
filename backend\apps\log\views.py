import csv
import logging
import datetime
from django.shortcuts import render
from django.http import HttpResponse
from django.db.models import Count, Avg
from django.utils import timezone
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import SystemLog, UserActionLog, SecurityLog, ApiRequestLog
from .serializers import (
    SystemLogSerializer, UserActionLogSerializer,
    SecurityLogSerializer, ApiRequestLogSerializer,
    LogVerificationSerializer
)
from apps.admin_management.views import IsAdminUser

logger = logging.getLogger(__name__)

class SystemLogViewSet(viewsets.ReadOnlyModelViewSet):
    """系统日志视图集"""
    queryset = SystemLog.objects.all().order_by('-created_at')
    serializer_class = SystemLogSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['module', 'action', 'description']
    ordering_fields = ['created_at', 'log_type', 'module', 'action']

    @action(detail=True, methods=['get'])
    def verify(self, request, pk=None):
        """验证日志完整性"""
        log = self.get_object()
        serializer = LogVerificationSerializer(data={
            'log_type': 'system',
            'log_id': log.id
        })
        serializer.is_valid(raise_exception=True)

        return Response({
            'is_valid': serializer.validated_data['is_valid'],
            'error': serializer.validated_data.get('error', None)
        })

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """导出系统日志为CSV格式"""
        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        log_type = request.query_params.get('log_type')
        module = request.query_params.get('module')

        # 构建查询条件
        queryset = self.get_queryset()
        if start_date:
            start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__gte=start_datetime)
        if end_date:
            end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__lte=end_datetime)
        if log_type:
            queryset = queryset.filter(log_type=log_type)
        if module:
            queryset = queryset.filter(module=module)

        # 创建CSV响应
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="system_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        # 创建CSV写入器
        writer = csv.writer(response)
        writer.writerow(['ID', '日志类型', '模块', '操作', '描述', 'IP地址', '用户ID', '用户类型', '创建时间', '哈希值', '前一条记录哈希值'])

        # 写入数据
        for log in queryset:
            writer.writerow([
                log.id,
                log.log_type,
                log.module,
                log.action,
                log.description,
                log.ip_address or '',
                log.user_id or '',
                log.user_type or '',
                log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                log.hash_value,
                log.prev_hash or ''
            ])

        return response

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取系统日志统计数据"""
        # 获取查询参数
        days = int(request.query_params.get('days', 7))

        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)

        # 按日期统计日志数量
        daily_counts = SystemLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).extra(
            select={'date': "DATE(created_at)"}
        ).values('date').annotate(count=Count('id')).order_by('date')

        # 按日志类型统计
        type_counts = SystemLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('log_type').annotate(count=Count('id')).order_by('-count')

        # 按模块统计
        module_counts = SystemLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('module').annotate(count=Count('id')).order_by('-count')

        return Response({
            'total_count': SystemLog.objects.filter(created_at__gte=start_date, created_at__lte=end_date).count(),
            'daily_counts': daily_counts,
            'type_counts': type_counts,
            'module_counts': module_counts
        })


class UserActionLogViewSet(viewsets.ReadOnlyModelViewSet):
    """用户操作日志视图集"""
    queryset = UserActionLog.objects.all().order_by('-created_at')
    serializer_class = UserActionLogSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['user__student_id_hash', 'action', 'description']
    ordering_fields = ['created_at', 'action', 'status']

    @action(detail=True, methods=['get'])
    def verify(self, request, pk=None):
        """验证日志完整性"""
        log = self.get_object()
        serializer = LogVerificationSerializer(data={
            'log_type': 'user_action',
            'log_id': log.id
        })
        serializer.is_valid(raise_exception=True)

        return Response({
            'is_valid': serializer.validated_data['is_valid'],
            'error': serializer.validated_data.get('error', None)
        })

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """导出用户操作日志为CSV格式"""
        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        action = request.query_params.get('action')
        status = request.query_params.get('status')
        user_id = request.query_params.get('user_id')

        # 构建查询条件
        queryset = self.get_queryset()
        if start_date:
            start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__gte=start_datetime)
        if end_date:
            end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__lte=end_datetime)
        if action:
            queryset = queryset.filter(action=action)
        if status:
            queryset = queryset.filter(status=status)
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # 创建CSV响应
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="user_action_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        # 创建CSV写入器
        writer = csv.writer(response)
        writer.writerow(['ID', '用户ID', '学号哈希', '操作', '描述', 'IP地址', '用户代理', '目标类型', '目标ID', '状态', '创建时间', '哈希值', '前一条记录哈希值'])

        # 写入数据
        for log in queryset:
            writer.writerow([
                log.id,
                log.user_id,
                log.user.student_id_hash if log.user else '',
                log.action,
                log.description,
                log.ip_address,
                log.user_agent,
                log.target_type or '',
                log.target_id or '',
                log.status,
                log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                log.hash_value,
                log.prev_hash or ''
            ])

        return response

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取用户操作日志统计数据"""
        # 获取查询参数
        days = int(request.query_params.get('days', 7))

        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)

        # 按日期统计日志数量
        daily_counts = UserActionLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).extra(
            select={'date': "DATE(created_at)"}
        ).values('date').annotate(count=Count('id')).order_by('date')

        # 按操作类型统计
        action_counts = UserActionLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('action').annotate(count=Count('id')).order_by('-count')

        # 按状态统计
        status_counts = UserActionLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('status').annotate(count=Count('id')).order_by('-count')

        # 按用户统计
        user_counts = UserActionLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('user_id').annotate(count=Count('id')).order_by('-count')[:10]

        return Response({
            'total_count': UserActionLog.objects.filter(created_at__gte=start_date, created_at__lte=end_date).count(),
            'daily_counts': daily_counts,
            'action_counts': action_counts,
            'status_counts': status_counts,
            'user_counts': user_counts
        })


class SecurityLogViewSet(viewsets.ReadOnlyModelViewSet):
    """安全日志视图集"""
    queryset = SecurityLog.objects.all().order_by('-created_at')
    serializer_class = SecurityLogSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['log_type', 'description', 'ip_address']
    ordering_fields = ['created_at', 'log_type', 'status']

    @action(detail=True, methods=['get'])
    def verify(self, request, pk=None):
        """验证日志完整性"""
        log = self.get_object()
        serializer = LogVerificationSerializer(data={
            'log_type': 'security',
            'log_id': log.id
        })
        serializer.is_valid(raise_exception=True)

        return Response({
            'is_valid': serializer.validated_data['is_valid'],
            'error': serializer.validated_data.get('error', None)
        })

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """导出安全日志为CSV格式"""
        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        log_type = request.query_params.get('log_type')
        status = request.query_params.get('status')

        # 构建查询条件
        queryset = self.get_queryset()
        if start_date:
            start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__gte=start_datetime)
        if end_date:
            end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__lte=end_datetime)
        if log_type:
            queryset = queryset.filter(log_type=log_type)
        if status:
            queryset = queryset.filter(status=status)

        # 创建CSV响应
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="security_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        # 创建CSV写入器
        writer = csv.writer(response)
        writer.writerow(['ID', '日志类型', '用户ID', '用户类型', 'IP地址', '用户代理', '描述', '状态', '创建时间', '哈希值', '前一条记录哈希值'])

        # 写入数据
        for log in queryset:
            writer.writerow([
                log.id,
                log.log_type,
                log.user_id or '',
                log.user_type or '',
                log.ip_address,
                log.user_agent,
                log.description,
                log.status,
                log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                log.hash_value,
                log.prev_hash or ''
            ])

        return response

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取安全日志统计数据"""
        # 获取查询参数
        days = int(request.query_params.get('days', 7))

        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)

        # 按日期统计日志数量
        daily_counts = SecurityLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).extra(
            select={'date': "DATE(created_at)"}
        ).values('date').annotate(count=Count('id')).order_by('date')

        # 按日志类型统计
        type_counts = SecurityLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('log_type').annotate(count=Count('id')).order_by('-count')

        # 按状态统计
        status_counts = SecurityLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('status').annotate(count=Count('id')).order_by('-count')

        # 按IP地址统计
        ip_counts = SecurityLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('ip_address').annotate(count=Count('id')).order_by('-count')[:10]

        return Response({
            'total_count': SecurityLog.objects.filter(created_at__gte=start_date, created_at__lte=end_date).count(),
            'daily_counts': daily_counts,
            'type_counts': type_counts,
            'status_counts': status_counts,
            'ip_counts': ip_counts
        })


class ApiRequestLogViewSet(viewsets.ReadOnlyModelViewSet):
    """API请求日志视图集"""
    queryset = ApiRequestLog.objects.all().order_by('-created_at')
    serializer_class = ApiRequestLogSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['request_id', 'path', 'ip_address']
    ordering_fields = ['created_at', 'method', 'response_status', 'response_time']

    @action(detail=True, methods=['get'])
    def verify(self, request, pk=None):
        """验证日志完整性"""
        log = self.get_object()
        serializer = LogVerificationSerializer(data={
            'log_type': 'api_request',
            'log_id': log.id
        })
        serializer.is_valid(raise_exception=True)

        return Response({
            'is_valid': serializer.validated_data['is_valid'],
            'error': serializer.validated_data.get('error', None)
        })

    @action(detail=False, methods=['get'])
    def export_csv(self, request):
        """导出API请求日志为CSV格式"""
        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        method = request.query_params.get('method')
        status_code = request.query_params.get('status_code')
        path = request.query_params.get('path')

        # 构建查询条件
        queryset = self.get_queryset()
        if start_date:
            start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__gte=start_datetime)
        if end_date:
            end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__lte=end_datetime)
        if method:
            queryset = queryset.filter(method=method)
        if status_code:
            queryset = queryset.filter(response_status=status_code)
        if path:
            queryset = queryset.filter(path__contains=path)

        # 创建CSV响应
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="api_request_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        # 创建CSV写入器
        writer = csv.writer(response)
        writer.writerow(['ID', '请求ID', '用户ID', '用户类型', '方法', '路径', '查询参数', '响应状态码', '响应时间(ms)', 'IP地址', '创建时间', '哈希值', '前一条记录哈希值'])

        # 写入数据
        for log in queryset:
            writer.writerow([
                log.id,
                log.request_id,
                log.user_id or '',
                log.user_type or '',
                log.method,
                log.path,
                log.query_params or '',
                log.response_status,
                log.response_time,
                log.ip_address,
                log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                log.hash_value,
                log.prev_hash or ''
            ])

        return response

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取API请求日志统计数据"""
        # 获取查询参数
        days = int(request.query_params.get('days', 7))

        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)

        # 按日期统计日志数量
        daily_counts = ApiRequestLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).extra(
            select={'date': "DATE(created_at)"}
        ).values('date').annotate(count=Count('id')).order_by('date')

        # 按HTTP方法统计
        method_counts = ApiRequestLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('method').annotate(count=Count('id')).order_by('-count')

        # 按响应状态码统计
        status_counts = ApiRequestLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('response_status').annotate(count=Count('id')).order_by('-count')

        # 按路径统计
        path_counts = ApiRequestLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('path').annotate(count=Count('id')).order_by('-count')[:10]

        # 平均响应时间
        avg_response_time = ApiRequestLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).aggregate(avg_time=Avg('response_time'))

        return Response({
            'total_count': ApiRequestLog.objects.filter(created_at__gte=start_date, created_at__lte=end_date).count(),
            'daily_counts': daily_counts,
            'method_counts': method_counts,
            'status_counts': status_counts,
            'path_counts': path_counts,
            'avg_response_time': avg_response_time['avg_time']
        })
