# 图书馆自习室管理系统

基于国密算法的图书馆自习室座位管理系统，采用Django + Vue.js开发。

## 项目概述

本系统旨在解决高校图书馆自习室座位管理的痛点问题，包括"占座"现象、座位利用率低、管理效率不高等问题。通过引入国密算法（SM2/SM3/SM4）保障系统安全，实现座位预约、签到、管理等功能，提高自习室资源利用率，改善学生学习体验。

## 技术栈

### 后端
- Django 4.x + Django REST Framework
- MySQL 8.x (使用pymysql连接)
- Redis 6.x (可选，用于缓存和消息队列)
- Channels (WebSocket支持)
- gmssl (国密算法库)

### 前端
- Vue 3 + Element Plus
- sm-crypto (前端国密算法库)
- axios (HTTP请求)
- vue-router (路由管理)
- vuex (状态管理)

## 项目结构

```
library_seat_system/
├── seat_management/        # Django项目主目录
├── authentication/         # 用户认证模块
├── seat/                   # 座位管理模块
├── log/                    # 日志管理模块
├── admin_module/          # 管理员模块
├── utils/                  # 工具类
│   ├── crypto/             # 国密算法工具
│   └── exception_handler.py # 异常处理器
├── keys/                   # 密钥存储目录
├── logs/                   # 日志存储目录
├── staticfiles/            # 静态文件
├── templates/              # 模板文件
├── frontend/               # Vue前端项目
├── manage.py               # Django管理脚本
└── requirements.txt        # Python依赖
```

## 安装与配置

### 后端环境配置

1. 创建并激活虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置数据库：
```bash
# 创建MySQL数据库
mysql -u root -p
CREATE DATABASE library_seat_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

4. 迁移数据库：
```bash
python manage.py makemigrations
python manage.py migrate
```

5. 创建超级用户：
```bash
python manage.py createsuperuser
```

6. 生成系统SM2密钥对：
```bash
python manage.py generate_sm2_keys
```

### 前端环境配置

1. 安装依赖：
```bash
cd frontend
npm install
```

2. 开发模式运行：
```bash
npm run serve
```

3. 构建生产版本：
```bash
npm run build
```

## 运行项目

1. 启动后端服务：
```bash
python manage.py runserver
```

2. 启动前端开发服务器：
```bash
cd frontend
npm run serve
```

## API文档

启动后端服务后，可以通过以下URL访问API文档：

- Swagger UI: http://localhost:8000/api/docs/
- ReDoc: http://localhost:8000/api/redoc/

## 贡献指南

1. Fork本仓库
2. 创建特性分支：`git checkout -b feature/your-feature-name`
3. 提交更改：`git commit -m 'Add some feature'`
4. 推送到分支：`git push origin feature/your-feature-name`
5. 提交Pull Request

## 许可证

[MIT](LICENSE)
