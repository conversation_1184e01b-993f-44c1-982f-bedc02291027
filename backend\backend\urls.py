"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# API路由
api_patterns = [
    # 用户认证相关API
    path('auth/', include('apps.authentication.urls')),

    # 座位管理相关API
    path('seat/', include('apps.seat.urls')),

    # 管理员相关API
    path('admin/', include('apps.admin_management.urls')),

    # 日志相关API
    path('log/', include('apps.log.urls')),

    # 国密算法相关API
    path('crypto/', include('apps.crypto.urls')),
]

urlpatterns = [
    # Django管理后台
    path('django-admin/', admin.site.urls),

    # API路由
    path('api/v1/', include(api_patterns)),
]

# 开发环境下提供媒体文件访问
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
