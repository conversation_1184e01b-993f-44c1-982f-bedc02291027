/**
 * 日期时间工具类
 */

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = "YYYY-MM-DD") {
  if (!date) return "";

  const d = new Date(date);

  if (isNaN(d.getTime())) {
    console.error("Invalid date:", date);
    return "";
  }

  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hours = d.getHours();
  const minutes = d.getMinutes();
  const seconds = d.getSeconds();

  const padZero = (num) => (num < 10 ? `0${num}` : num);

  return format
    .replace("YYYY", year)
    .replace("MM", padZero(month))
    .replace("DD", padZero(day))
    .replace("HH", padZero(hours))
    .replace("mm", padZero(minutes))
    .replace("ss", padZero(seconds));
}

/**
 * 格式化日期时间
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 格式化后的日期时间字符串，格式为 'YYYY-MM-DD HH:mm:ss'
 */
export function formatDateTime(date) {
  return formatDate(date, "YYYY-MM-DD HH:mm:ss");
}

/**
 * 格式化时间
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 格式化后的时间字符串，格式为 'HH:mm:ss'
 */
export function formatTime(date) {
  return formatDate(date, "HH:mm:ss");
}

/**
 * 计算两个日期之间的差值
 * @param {Date|string|number} startDate - 开始日期
 * @param {Date|string|number} endDate - 结束日期
 * @returns {Object} 包含天、小时、分钟、秒的对象
 */
export function dateDiff(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.error("Invalid date:", { startDate, endDate });
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }

  const diffMs = end - start;
  const diffSecs = Math.floor(diffMs / 1000);
  const days = Math.floor(diffSecs / 86400);
  const hours = Math.floor((diffSecs % 86400) / 3600);
  const minutes = Math.floor((diffSecs % 3600) / 60);
  const seconds = diffSecs % 60;

  return { days, hours, minutes, seconds };
}

/**
 * 格式化时间差
 * @param {Date|string|number} startDate - 开始日期
 * @param {Date|string|number} endDate - 结束日期
 * @returns {string} 格式化后的时间差字符串
 */
export function formatDateDiff(startDate, endDate) {
  const { days, hours, minutes, seconds } = dateDiff(startDate, endDate);

  if (days > 0) {
    return `${days}天${hours}小时${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
}

/**
 * 获取今天的日期字符串
 * @returns {string} 今天的日期字符串，格式为 'YYYY-MM-DD'
 */
export function getToday() {
  return formatDate(new Date());
}

/**
 * 获取明天的日期字符串
 * @returns {string} 明天的日期字符串，格式为 'YYYY-MM-DD'
 */
export function getTomorrow() {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  return formatDate(tomorrow);
}

/**
 * 获取昨天的日期字符串
 * @returns {string} 昨天的日期字符串，格式为 'YYYY-MM-DD'
 */
export function getYesterday() {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return formatDate(yesterday);
}

/**
 * 获取指定天数后的日期
 * @param {number} days - 天数，可以为负数
 * @returns {string} 指定天数后的日期字符串，格式为 'YYYY-MM-DD'
 */
export function getDateAfter(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return formatDate(date);
}

/**
 * 获取指定日期是星期几
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 星期几的字符串
 */
export function getDayOfWeek(date) {
  const d = new Date(date);

  if (isNaN(d.getTime())) {
    console.error("Invalid date:", date);
    return "";
  }

  const weekdays = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ];
  return weekdays[d.getDay()];
}

/**
 * 获取指定月份的天数
 * @param {number} year - 年份
 * @param {number} month - 月份（1-12）
 * @returns {number} 该月的天数
 */
export function getDaysInMonth(year, month) {
  return new Date(year, month, 0).getDate();
}

/**
 * 判断是否为闰年
 * @param {number} year - 年份
 * @returns {boolean} 是否为闰年
 */
export function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
}

/**
 * 获取日期范围内的所有日期
 * @param {Date|string|number} startDate - 开始日期
 * @param {Date|string|number} endDate - 结束日期
 * @returns {Array<string>} 日期范围内的所有日期字符串数组，格式为 'YYYY-MM-DD'
 */
export function getDateRange(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    console.error("Invalid date:", { startDate, endDate });
    return [];
  }

  const dates = [];
  const currentDate = new Date(start);

  while (currentDate <= end) {
    dates.push(formatDate(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

/**
 * 解析日期字符串
 * @param {string} dateString - 日期字符串
 * @param {string} format - 日期格式，默认为 'YYYY-MM-DD'
 * @returns {Date} 解析后的日期对象
 */
export function parseDate(dateString, format = "YYYY-MM-DD") {
  if (!dateString) return null;

  const parts = {};
  const formatParts = format.match(/YYYY|MM|DD|HH|mm|ss/g);
  const dateParts = dateString.split(/[-\s:]/);

  if (formatParts.length !== dateParts.length) {
    console.error("Date format mismatch:", { dateString, format });
    return null;
  }

  formatParts.forEach((part, index) => {
    parts[part] = parseInt(dateParts[index], 10);
  });

  const year = parts.YYYY || new Date().getFullYear();
  const month = (parts.MM || 1) - 1;
  const day = parts.DD || 1;
  const hours = parts.HH || 0;
  const minutes = parts.mm || 0;
  const seconds = parts.ss || 0;

  return new Date(year, month, day, hours, minutes, seconds);
}
