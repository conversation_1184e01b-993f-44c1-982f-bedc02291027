from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from django.db.models import Q
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Room, Seat, Reservation, SeatOperation, BlacklistRecord
from .serializers import (
    RoomSerializer, SeatSerializer, SeatDetailSerializer,
    ReservationSerializer, ReservationCheckInSerializer,
    ReservationCheckOutSerializer, SeatOperationSerializer
)
from .utils import (
    check_seat_availability, create_seat_operation,
    verify_seat_operation_chain
)
from apps.authentication.models import User, CreditRecord
from apps.authentication.utils import update_credit_score, get_client_ip
from utils.crypto import SM3Hasher, SM2Crypto
import datetime
import logging

logger = logging.getLogger(__name__)

class RoomViewSet(viewsets.ReadOnlyModelViewSet):
    """自习室视图集"""
    queryset = Room.objects.filter(status='open')
    serializer_class = RoomSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'location']
    ordering_fields = ['floor', 'capacity', 'name']
    ordering = ['floor', 'name']

    @action(detail=True, methods=['get'])
    def seats(self, request, pk=None):
        """获取自习室的座位列表"""
        room = self.get_object()
        seats = Seat.objects.filter(room=room)

        # 过滤参数
        status = request.query_params.get('status')
        if status:
            seats = seats.filter(status=status)

        power_outlet = request.query_params.get('power_outlet')
        if power_outlet:
            seats = seats.filter(is_power_outlet=(power_outlet.lower() == 'true'))

        window_seat = request.query_params.get('window_seat')
        if window_seat:
            seats = seats.filter(is_window_seat=(window_seat.lower() == 'true'))

        serializer = SeatSerializer(seats, many=True)
        return Response(serializer.data)


class SeatViewSet(viewsets.ReadOnlyModelViewSet):
    """座位视图集"""
    queryset = Seat.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['seat_number', 'room__name']
    ordering_fields = ['room', 'row', 'column']
    ordering = ['room', 'row', 'column']

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return SeatDetailSerializer
        return SeatSerializer

    @action(detail=True, methods=['get'])
    def availability(self, request, pk=None):
        """查询座位在指定时间段的可用性"""
        seat = self.get_object()

        # 获取查询参数
        date_str = request.query_params.get('date')
        if not date_str:
            date_str = timezone.now().date().isoformat()

        try:
            query_date = datetime.date.fromisoformat(date_str)
        except ValueError:
            return Response({'error': '无效的日期格式'}, status=status.HTTP_400_BAD_REQUEST)

        # 获取该座位在指定日期的所有预约
        start_of_day = datetime.datetime.combine(query_date, datetime.time.min, tzinfo=timezone.get_current_timezone())
        end_of_day = datetime.datetime.combine(query_date, datetime.time.max, tzinfo=timezone.get_current_timezone())

        reservations = Reservation.objects.filter(
            seat=seat,
            status__in=['pending', 'checked_in'],
            start_time__lt=end_of_day,
            end_time__gt=start_of_day
        ).order_by('start_time')

        # 获取自习室开放时间
        room = seat.room
        open_time = datetime.datetime.combine(query_date, room.open_time, tzinfo=timezone.get_current_timezone())
        close_time = datetime.datetime.combine(query_date, room.close_time, tzinfo=timezone.get_current_timezone())

        # 构建时间段列表
        time_slots = []
        current_time = open_time

        while current_time < close_time:
            next_time = current_time + datetime.timedelta(minutes=30)
            if next_time > close_time:
                next_time = close_time

            # 检查该时间段是否有预约
            is_available = True
            for reservation in reservations:
                if reservation.start_time < next_time and reservation.end_time > current_time:
                    is_available = False
                    break

            time_slots.append({
                'start_time': current_time.isoformat(),
                'end_time': next_time.isoformat(),
                'is_available': is_available
            })

            current_time = next_time

        return Response({
            'seat': SeatSerializer(seat).data,
            'date': date_str,
            'time_slots': time_slots
        })


class ReservationViewSet(viewsets.ModelViewSet):
    """预约视图集"""
    serializer_class = ReservationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        return Reservation.objects.filter(user=user).order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save()

    @action(detail=False, methods=['post'])
    def check_in(self, request):
        """预约签到"""
        serializer = ReservationCheckInSerializer(data=request.data)
        if serializer.is_valid():
            reservation = serializer.validated_data['reservation']

            # 验证预约状态
            if reservation.status != 'pending':
                return Response({'error': f'预约状态错误: {reservation.status}'}, status=status.HTTP_400_BAD_REQUEST)

            # 验证预约时间
            now = timezone.now()
            if now < reservation.start_time:
                return Response({'error': '预约尚未开始'}, status=status.HTTP_400_BAD_REQUEST)

            if now > reservation.end_time:
                return Response({'error': '预约已过期'}, status=status.HTTP_400_BAD_REQUEST)

            # 验证签名（如果提供）
            signature = serializer.validated_data.get('signature')
            if signature and reservation.signature:
                # 验证用户是否有公钥
                user = request.user
                if not user.public_key:
                    return Response({'error': '用户未注册SM2公钥'}, status=status.HTTP_400_BAD_REQUEST)

                # 验证签名
                if not SM2Crypto.verify(user.public_key, reservation.reservation_code, signature):
                    return Response({'error': '签名验证失败'}, status=status.HTTP_400_BAD_REQUEST)

            # 更新预约状态
            reservation.status = 'checked_in'
            reservation.check_in_time = now
            reservation.save()

            # 更新座位状态
            seat = reservation.seat

            # 记录座位操作
            create_seat_operation(
                seat=seat,
                operation_type='check_in',
                user=request.user,
                reservation=reservation,
                ip_address=get_client_ip(request)
            )

            # 增加信誉分（准时签到）
            if now < reservation.start_time + datetime.timedelta(minutes=10):
                update_credit_score(
                    user=request.user,
                    delta=1,
                    reason="准时签到",
                    related_entity='reservation',
                    related_id=reservation.id
                )

            return Response({
                'message': '签到成功',
                'reservation': ReservationSerializer(reservation).data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def check_out(self, request):
        """预约签退"""
        serializer = ReservationCheckOutSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            reservation = serializer.validated_data['reservation']

            # 验证预约状态
            if reservation.status != 'checked_in':
                return Response({'error': f'预约状态错误: {reservation.status}'}, status=status.HTTP_400_BAD_REQUEST)

            # 验证签名（如果提供）
            signature = serializer.validated_data.get('signature')
            if signature and reservation.signature:
                # 验证用户是否有公钥
                user = request.user
                if not user.public_key:
                    return Response({'error': '用户未注册SM2公钥'}, status=status.HTTP_400_BAD_REQUEST)

                # 验证签名
                if not SM2Crypto.verify(user.public_key, reservation.reservation_code, signature):
                    return Response({'error': '签名验证失败'}, status=status.HTTP_400_BAD_REQUEST)

            now = timezone.now()

            # 更新预约状态
            reservation.status = 'completed'
            reservation.check_out_time = now
            reservation.save()

            # 更新座位状态
            seat = reservation.seat

            # 记录座位操作
            create_seat_operation(
                seat=seat,
                operation_type='check_out',
                user=request.user,
                reservation=reservation,
                ip_address=get_client_ip(request)
            )

            # 增加用户信誉分（按时签退）
            # 如果是提前签退，不增加信誉分
            if now >= reservation.end_time - datetime.timedelta(minutes=10):
                update_credit_score(
                    user=request.user,
                    delta=1,
                    reason='按时签退',
                    related_entity='reservation',
                    related_id=reservation.id
                )

            return Response({
                'message': '签退成功',
                'reservation': ReservationSerializer(reservation).data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消预约"""
        reservation = self.get_object()

        # 验证预约状态
        if reservation.status != 'pending':
            return Response({'error': f'预约状态不允许取消: {reservation.status}'},
                           status=status.HTTP_400_BAD_REQUEST)

        # 验证签名（如果提供）
        signature = request.data.get('signature')
        if signature and reservation.signature:
            # 验证用户是否有公钥
            user = request.user
            if not user.public_key:
                return Response({'error': '用户未注册SM2公钥'}, status=status.HTTP_400_BAD_REQUEST)

            # 验证签名
            if not SM2Crypto.verify(user.public_key, reservation.reservation_code, signature):
                return Response({'error': '签名验证失败'}, status=status.HTTP_400_BAD_REQUEST)

        # 更新预约状态
        reservation.status = 'cancelled'
        reservation.save()

        # 更新座位状态
        seat = reservation.seat

        # 记录座位操作
        create_seat_operation(
            seat=seat,
            operation_type='cancel',
            user=request.user,
            reservation=reservation,
            ip_address=get_client_ip(request)
        )

        # 检查是否是提前取消
        now = timezone.now()
        if now < reservation.start_time - datetime.timedelta(hours=1):
            # 提前1小时以上取消，不扣信誉分
            pass
        else:
            # 临时取消，扣除信誉分
            update_credit_score(
                user=request.user,
                delta=-5,
                reason='临时取消预约',
                related_entity='reservation',
                related_id=reservation.id
            )

        return Response({
            'message': '预约已取消',
            'reservation': ReservationSerializer(reservation).data
        })

    @action(detail=False, methods=['get'])
    def active(self, request):
        """获取当前用户的活跃预约"""
        user = request.user
        now = timezone.now()

        active_reservation = Reservation.objects.filter(
            user=user,
            status__in=['pending', 'checked_in'],
            end_time__gt=now
        ).first()

        if active_reservation:
            serializer = ReservationSerializer(active_reservation)
            return Response(serializer.data)
        return Response({'message': '当前没有活跃的预约'}, status=status.HTTP_404_NOT_FOUND)


class SeatOperationViewSet(viewsets.ReadOnlyModelViewSet):
    """座位操作记录视图集"""
    serializer_class = SeatOperationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        return SeatOperation.objects.filter(user=user).order_by('-created_at')
