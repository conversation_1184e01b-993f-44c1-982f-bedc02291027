import axios from "axios";
import { ElMessage } from "element-plus";
import router from "@/router";
import { SM4Crypto } from "@/utils/crypto";

// 创建axios实例
const http = axios.create({
  baseURL: process.env.VUE_APP_API_URL || "/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem("token");

    // 如果有token，则添加到请求头
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    // 检查是否需要加密请求数据
    if (config.encrypt && config.data) {
      // 获取SM4密钥
      const sm4Key = localStorage.getItem("sm4Key");

      if (sm4Key) {
        // 加密请求数据
        const encryptedData = SM4Crypto.encrypt(
          JSON.stringify(config.data),
          sm4Key
        );

        // 替换请求数据
        config.data = { encrypted: encryptedData };

        // 添加加密标记
        config.headers["X-Encrypted"] = "true";
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 检查是否是加密响应
    if (response.headers["x-encrypted"] === "true" && response.data.encrypted) {
      // 获取SM4密钥
      const sm4Key = localStorage.getItem("sm4Key");

      if (sm4Key) {
        try {
          // 解密响应数据
          const decryptedData = SM4Crypto.decrypt(
            response.data.encrypted,
            sm4Key
          );

          // 解析JSON
          response.data = JSON.parse(decryptedData);
        } catch (error) {
          console.error("解密响应数据失败:", error);
          return Promise.reject(new Error("解密响应数据失败"));
        }
      }
    }

    return response;
  },
  (error) => {
    if (error.response) {
      // 处理响应错误
      switch (error.response.status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem("token");
          localStorage.removeItem("userInfo");

          // 如果不是登录页，则跳转到登录页
          if (router.currentRoute.value.path !== "/login") {
            ElMessage.error("登录已过期，请重新登录");
            router.push("/login");
          }
          break;

        case 403:
          // 禁止访问
          ElMessage.error("没有权限访问该资源");
          break;

        case 404:
          // 资源不存在
          ElMessage.error("请求的资源不存在");
          break;

        case 500:
          // 服务器错误
          ElMessage.error("服务器错误，请稍后重试");
          break;

        default:
          // 其他错误
          if (error.response.data && error.response.data.message) {
            ElMessage.error(error.response.data.message);
          } else {
            ElMessage.error("请求失败，请稍后重试");
          }
      }
    } else if (error.request) {
      // 请求发送但没有收到响应
      ElMessage.error("网络错误，请检查网络连接");
    } else {
      // 请求配置错误
      ElMessage.error("请求配置错误");
    }

    return Promise.reject(error);
  }
);

// 导出请求方法
export default {
  // GET请求
  get(url, params = {}, config = {}) {
    return http.get(url, { params, ...config });
  },

  // POST请求
  post(url, data = {}, config = {}) {
    return http.post(url, data, config);
  },

  // PUT请求
  put(url, data = {}, config = {}) {
    return http.put(url, data, config);
  },

  // DELETE请求
  delete(url, config = {}) {
    return http.delete(url, config);
  },

  // 加密POST请求
  encryptedPost(url, data = {}, config = {}) {
    return http.post(url, data, { ...config, encrypt: true });
  },

  // 加密PUT请求
  encryptedPut(url, data = {}, config = {}) {
    return http.put(url, data, { ...config, encrypt: true });
  },
};
