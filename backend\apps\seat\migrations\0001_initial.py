# Generated by Django 4.2.7 on 2025-05-21 05:17

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlacklistRecord',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('reason', models.CharField(max_length=255, verbose_name='加入黑名单原因')),
                ('start_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(verbose_name='结束时间')),
                ('status', models.CharField(default='active', max_length=20, verbose_name='状态(active/expired/cancelled)')),
                ('operator_id', models.BigIntegerField(blank=True, null=True, verbose_name='操作员ID')),
                ('operator_type', models.CharField(default='system', max_length=10, verbose_name='操作员类型(system/admin)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '黑名单记录',
                'verbose_name_plural': '黑名单记录',
                'db_table': 'blacklist_record',
            },
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('reservation_code', models.CharField(max_length=64, unique=True, verbose_name='预约码(SM3哈希)')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(verbose_name='结束时间')),
                ('status', models.CharField(default='pending', max_length=20, verbose_name='状态(pending/checked_in/completed/cancelled/timeout)')),
                ('check_in_time', models.DateTimeField(blank=True, null=True, verbose_name='签到时间')),
                ('check_out_time', models.DateTimeField(blank=True, null=True, verbose_name='签退时间')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('signature', models.CharField(blank=True, max_length=128, null=True, verbose_name='SM2签名')),
            ],
            options={
                'verbose_name': '预约',
                'verbose_name_plural': '预约',
                'db_table': 'reservation',
            },
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('name', models.CharField(max_length=100, verbose_name='自习室名称')),
                ('location', models.CharField(max_length=255, verbose_name='位置描述')),
                ('floor', models.IntegerField(verbose_name='楼层')),
                ('capacity', models.IntegerField(verbose_name='座位容量')),
                ('open_time', models.TimeField(verbose_name='开放时间')),
                ('close_time', models.TimeField(verbose_name='关闭时间')),
                ('status', models.CharField(default='open', max_length=20, verbose_name='状态(open/closed/maintenance)')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '自习室',
                'verbose_name_plural': '自习室',
                'db_table': 'room',
            },
        ),
        migrations.CreateModel(
            name='Seat',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('seat_number', models.CharField(max_length=20, verbose_name='座位编号')),
                ('row', models.IntegerField(verbose_name='排号')),
                ('column', models.IntegerField(verbose_name='列号')),
                ('status', models.CharField(default='available', max_length=20, verbose_name='状态(available/occupied/disabled)')),
                ('is_power_outlet', models.BooleanField(default=False, verbose_name='是否有电源')),
                ('is_window_seat', models.BooleanField(default=False, verbose_name='是否靠窗')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seat.room', verbose_name='所属自习室')),
            ],
            options={
                'verbose_name': '座位',
                'verbose_name_plural': '座位',
                'db_table': 'seat',
            },
        ),
        migrations.CreateModel(
            name='SeatOperation',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('operation_type', models.CharField(max_length=20, verbose_name='操作类型(reserve/check_in/check_out/cancel/timeout)')),
                ('status_before', models.CharField(max_length=20, verbose_name='操作前状态')),
                ('status_after', models.CharField(max_length=20, verbose_name='操作后状态')),
                ('ip_address', models.CharField(blank=True, max_length=45, null=True, verbose_name='IP地址')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('hash_value', models.CharField(max_length=64, verbose_name='SM3哈希值')),
                ('prev_hash', models.CharField(blank=True, max_length=64, null=True, verbose_name='前一条记录的哈希值')),
                ('reservation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='seat.reservation', verbose_name='关联预约')),
                ('seat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seat.seat', verbose_name='座位')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户')),
            ],
            options={
                'verbose_name': '座位操作记录',
                'verbose_name_plural': '座位操作记录',
                'db_table': 'seat_operation',
            },
        ),
        migrations.AddIndex(
            model_name='room',
            index=models.Index(fields=['status'], name='room_status_d732f6_idx'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='seat',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='seat.seat', verbose_name='座位'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='blacklistrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户'),
        ),
        migrations.AddIndex(
            model_name='seatoperation',
            index=models.Index(fields=['seat'], name='seat_operat_seat_id_2290d8_idx'),
        ),
        migrations.AddIndex(
            model_name='seatoperation',
            index=models.Index(fields=['operation_type'], name='seat_operat_operati_e87e82_idx'),
        ),
        migrations.AddIndex(
            model_name='seatoperation',
            index=models.Index(fields=['user'], name='seat_operat_user_id_2adb8a_idx'),
        ),
        migrations.AddIndex(
            model_name='seatoperation',
            index=models.Index(fields=['reservation'], name='seat_operat_reserva_7b05ba_idx'),
        ),
        migrations.AddIndex(
            model_name='seatoperation',
            index=models.Index(fields=['created_at'], name='seat_operat_created_f4133d_idx'),
        ),
        migrations.AddIndex(
            model_name='seat',
            index=models.Index(fields=['room', 'status'], name='seat_room_id_3053d0_idx'),
        ),
        migrations.AddIndex(
            model_name='seat',
            index=models.Index(fields=['is_power_outlet'], name='seat_is_powe_bc9114_idx'),
        ),
        migrations.AddIndex(
            model_name='seat',
            index=models.Index(fields=['is_window_seat'], name='seat_is_wind_45da19_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='seat',
            unique_together={('room', 'row', 'column'), ('room', 'seat_number')},
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['user'], name='reservation_user_id_7f8e69_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['seat'], name='reservation_seat_id_e7ccec_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['status'], name='reservation_status_88445b_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['start_time'], name='reservation_start_t_5021b2_idx'),
        ),
        migrations.AddIndex(
            model_name='reservation',
            index=models.Index(fields=['end_time'], name='reservation_end_tim_0be07a_idx'),
        ),
        migrations.AddIndex(
            model_name='blacklistrecord',
            index=models.Index(fields=['user'], name='blacklist_r_user_id_010c97_idx'),
        ),
        migrations.AddIndex(
            model_name='blacklistrecord',
            index=models.Index(fields=['status'], name='blacklist_r_status_2ef757_idx'),
        ),
        migrations.AddIndex(
            model_name='blacklistrecord',
            index=models.Index(fields=['start_time'], name='blacklist_r_start_t_a1a00c_idx'),
        ),
        migrations.AddIndex(
            model_name='blacklistrecord',
            index=models.Index(fields=['end_time'], name='blacklist_r_end_tim_6a0f4b_idx'),
        ),
    ]
