import { createStore } from "vuex";
import user from "./modules/user";
import seat from "./modules/seat";

export default createStore({
  state: {
    appName: "图书馆自习室管理系统",
    appVersion: "1.0.0",
    loading: false,
    error: null,
  },
  getters: {
    appName: (state) => state.appName,
    appVersion: (state) => state.appVersion,
    isLoading: (state) => state.loading,
    error: (state) => state.error,
  },
  mutations: {
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    SET_ERROR(state, error) {
      state.error = error;
    },
    CLEAR_ERROR(state) {
      state.error = null;
    },
  },
  actions: {
    setLoading({ commit }, loading) {
      commit("SET_LOADING", loading);
    },
    setError({ commit }, error) {
      commit("SET_ERROR", error);
    },
    clearError({ commit }) {
      commit("CLEAR_ERROR");
    },
  },
  modules: {
    user,
    seat,
  },
});
