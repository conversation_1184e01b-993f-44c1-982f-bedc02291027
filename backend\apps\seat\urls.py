from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器并注册视图集
router = DefaultRouter()
router.register(r'rooms', views.RoomViewSet)
router.register(r'seats', views.SeatViewSet)
router.register(r'reservations', views.ReservationViewSet, basename='reservation')
router.register(r'operations', views.SeatOperationViewSet, basename='seat-operation')

urlpatterns = [
    # 包含路由器生成的URL
    path('', include(router.urls)),
]
