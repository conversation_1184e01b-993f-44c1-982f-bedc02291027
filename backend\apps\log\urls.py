from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器并注册视图集
router = DefaultRouter()
router.register(r'system', views.SystemLogViewSet)
router.register(r'user-actions', views.UserActionLogViewSet)
router.register(r'security', views.SecurityLogViewSet)
router.register(r'api-requests', views.ApiRequestLogViewSet)

urlpatterns = [
    # 包含路由器生成的URL
    path('', include(router.urls)),
]
