from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.log.models import ApiRequestLog
from apps.admin_management.models import Admin
from utils.crypto import SM3Hasher
import random
import datetime
import uuid
import ipaddress
import json
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '创建模拟API请求日志数据'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=50, help='要创建的日志数量')
        parser.add_argument('--days', type=int, default=7, help='日志分布的天数')
        parser.add_argument('--error-rate', type=float, default=0.2, help='错误请求的比例(0-1)')

    def handle(self, *args, **options):
        count = options['count']
        days = options['days']
        error_rate = options['error_rate']
        
        # 获取所有管理员
        admins = list(Admin.objects.all())
        
        self.stdout.write(f'开始创建 {count} 条模拟API请求日志...')
        
        # HTTP方法列表
        methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
        
        # API路径列表
        api_paths = [
            '/api/admin/login/',
            '/api/admin/logout/',
            '/api/admin/profile/',
            '/api/admin/configs/',
            '/api/admin/logs/',
            '/api/rooms/',
            '/api/seats/',
            '/api/users/',
            '/api/blacklist/',
            '/api/reservations/',
            '/api/statistics/'
        ]
        
        # 响应状态码列表
        success_status_codes = [200, 201, 204]
        error_status_codes = [400, 401, 403, 404, 500]
        
        # 生成随机IP地址
        def random_ip():
            return str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))
        
        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)
        
        created_count = 0
        error_count = 0
        
        for i in range(count):
            try:
                # 随机选择HTTP方法
                method = random.choice(methods)
                
                # 随机选择API路径
                path = random.choice(api_paths)
                if path.endswith('/') and random.random() < 0.3:
                    path += str(random.randint(1, 100)) + '/'
                
                # 决定是否为错误请求
                is_error = random.random() < error_rate
                
                # 随机选择响应状态码
                response_status = random.choice(error_status_codes) if is_error else random.choice(success_status_codes)
                
                # 随机生成响应时间(50-500ms)
                response_time = random.randint(50, 500)
                
                # 随机选择用户类型和ID
                user_type = 'admin' if random.random() < 0.7 else 'user'
                user_id = random.choice(admins).id if user_type == 'admin' and admins else random.randint(1, 100)
                
                # 随机生成查询参数
                query_params = None
                if random.random() < 0.6:
                    params = {}
                    if random.random() < 0.5:
                        params['page'] = random.randint(1, 10)
                    if random.random() < 0.5:
                        params['limit'] = random.choice([10, 20, 50, 100])
                    if random.random() < 0.3:
                        params['sort'] = random.choice(['created_at', 'id', 'name', 'status'])
                    if random.random() < 0.3:
                        params['order'] = random.choice(['asc', 'desc'])
                    if params:
                        query_params = json.dumps(params)
                
                # 随机生成请求体
                request_body = None
                if method in ['POST', 'PUT', 'PATCH'] and random.random() < 0.8:
                    body = {}
                    if 'login' in path:
                        body = {
                            'username': f'admin{random.randint(1, 5)}',
                            'password': '******'
                        }
                    elif 'configs' in path:
                        body = {
                            'key': f'config_{random.randint(1, 10)}',
                            'value': f'value_{random.randint(1, 100)}',
                            'description': f'配置项描述 {random.randint(1, 100)}'
                        }
                    elif 'rooms' in path:
                        body = {
                            'name': f'自习室 {random.randint(1, 8)}',
                            'location': f'图书馆 {random.randint(1, 8)} 楼',
                            'capacity': random.choice([36, 48, 64])
                        }
                    request_body = json.dumps(body)
                
                # 随机生成创建时间
                random_seconds = random.randint(0, int((end_date - start_date).total_seconds()))
                created_at = start_date + datetime.timedelta(seconds=random_seconds)
                
                # 生成请求ID
                request_id = str(uuid.uuid4())
                
                # 生成IP地址
                ip_address = random_ip()
                
                # 生成用户代理
                user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                
                # 准备日志数据用于哈希计算
                log_data = {
                    'request_id': request_id,
                    'user_id': user_id,
                    'user_type': user_type,
                    'method': method,
                    'path': path,
                    'query_params': query_params,
                    'request_body': request_body,
                    'response_status': response_status,
                    'response_time': response_time,
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'created_at': created_at.isoformat(),
                }
                
                # 计算哈希值
                hash_value = SM3Hasher.hash(json.dumps(log_data, sort_keys=True))
                
                # 获取前一条日志的哈希值
                prev_log = ApiRequestLog.objects.order_by('-id').first()
                prev_hash = prev_log.hash_value if prev_log else None
                
                # 创建日志记录
                log = ApiRequestLog.objects.create(
                    request_id=request_id,
                    user_id=user_id,
                    user_type=user_type,
                    method=method,
                    path=path,
                    query_params=query_params,
                    request_body=request_body,
                    response_status=response_status,
                    response_time=response_time,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    created_at=created_at,
                    hash_value=hash_value,
                    prev_hash=prev_hash
                )
                
                created_count += 1
                if is_error:
                    error_count += 1
                
                # 每10条日志输出一次进度
                if (i + 1) % 10 == 0:
                    self.stdout.write(f'已创建 {i + 1}/{count} 条日志...')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'创建日志时出错: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'成功创建 {created_count} 条API请求日志，其中 {error_count} 条错误请求'))
