"""
自定义异常处理
"""
from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException
from rest_framework import status
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)

class CustomAPIException(APIException):
    """自定义API异常"""
    
    def __init__(self, detail=None, code=None, status_code=None):
        self.status_code = status_code or status.HTTP_400_BAD_REQUEST
        super().__init__(detail, code)


class AuthenticationFailedException(CustomAPIException):
    """认证失败异常"""
    
    def __init__(self, detail="认证失败", code="authentication_failed"):
        super().__init__(detail, code, status.HTTP_401_UNAUTHORIZED)


class PermissionDeniedException(CustomAPIException):
    """权限拒绝异常"""
    
    def __init__(self, detail="权限不足", code="permission_denied"):
        super().__init__(detail, code, status.HTTP_403_FORBIDDEN)


class ResourceNotFoundException(CustomAPIException):
    """资源不存在异常"""
    
    def __init__(self, detail="资源不存在", code="not_found"):
        super().__init__(detail, code, status.HTTP_404_NOT_FOUND)


class ValidationException(CustomAPIException):
    """数据验证异常"""
    
    def __init__(self, detail="数据验证失败", code="validation_error"):
        super().__init__(detail, code, status.HTTP_400_BAD_REQUEST)


class ConflictException(CustomAPIException):
    """数据冲突异常"""
    
    def __init__(self, detail="数据冲突", code="conflict"):
        super().__init__(detail, code, status.HTTP_409_CONFLICT)


class BusinessLogicException(CustomAPIException):
    """业务逻辑异常"""
    
    def __init__(self, detail="业务逻辑错误", code="business_logic_error"):
        super().__init__(detail, code, status.HTTP_400_BAD_REQUEST)


class CryptoException(CustomAPIException):
    """加密解密异常"""
    
    def __init__(self, detail="加密解密错误", code="crypto_error"):
        super().__init__(detail, code, status.HTTP_500_INTERNAL_SERVER_ERROR)


def custom_exception_handler(exc, context):
    """
    自定义异常处理
    
    参数:
        exc: 异常对象
        context: 上下文
        
    返回:
        Response对象
    """
    # 首先调用REST framework默认的异常处理
    response = exception_handler(exc, context)
    
    # 如果是自定义异常，直接返回
    if isinstance(exc, CustomAPIException):
        return response
    
    # 如果是REST framework的异常，但没有被处理
    if response is None:
        logger.error(f"Unhandled exception: {exc}")
        return Response(
            {
                "code": "server_error",
                "detail": "服务器内部错误",
                "message": str(exc) if str(exc) else "未知错误"
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    # 如果是REST framework的异常，已经被处理，但需要格式化
    if hasattr(response, 'data') and isinstance(response.data, dict):
        if 'detail' in response.data:
            response.data = {
                'code': getattr(exc, 'default_code', 'error'),
                'detail': response.data['detail'],
                'message': str(response.data['detail'])
            }
        else:
            errors = []
            for field, field_errors in response.data.items():
                if isinstance(field_errors, list):
                    for error in field_errors:
                        errors.append(f"{field}: {error}")
                else:
                    errors.append(f"{field}: {field_errors}")
            
            response.data = {
                'code': getattr(exc, 'default_code', 'validation_error'),
                'detail': errors,
                'message': "数据验证失败"
            }
    
    return response
