from django.contrib import admin
from .models import SystemLog, UserActionLog, SecurityLog, ApiRequestLog

class SystemLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'log_type', 'module', 'action', 'user_id', 'user_type', 'ip_address', 'created_at')
    list_filter = ('log_type', 'module')
    search_fields = ('description', 'module', 'action')
    ordering = ('-created_at',)

class UserActionLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'action', 'status', 'ip_address', 'target_type', 'target_id', 'created_at')
    list_filter = ('action', 'status')
    search_fields = ('user__student_id_hash', 'description', 'action')
    ordering = ('-created_at',)

class SecurityLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'log_type', 'user_id', 'user_type', 'status', 'ip_address', 'created_at')
    list_filter = ('log_type', 'status', 'user_type')
    search_fields = ('description', 'ip_address')
    ordering = ('-created_at',)

class ApiRequestLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'request_id', 'method', 'path', 'user_id', 'response_status', 'response_time', 'created_at')
    list_filter = ('method', 'response_status')
    search_fields = ('path', 'request_id', 'ip_address')
    ordering = ('-created_at',)

admin.site.register(SystemLog, SystemLogAdmin)
admin.site.register(UserActionLog, UserActionLogAdmin)
admin.site.register(SecurityLog, SecurityLogAdmin)
admin.site.register(ApiRequestLog, ApiRequestLogAdmin)
