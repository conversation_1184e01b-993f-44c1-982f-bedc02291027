# Generated by Django 4.2.7 on 2025-05-21 05:17

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('log_type', models.CharField(max_length=20, verbose_name='日志类型(info/warning/error/security)')),
                ('module', models.CharField(max_length=50, verbose_name='模块名称')),
                ('action', models.CharField(max_length=50, verbose_name='操作名称')),
                ('description', models.TextField(verbose_name='日志描述')),
                ('ip_address', models.CharField(blank=True, max_length=45, null=True, verbose_name='IP地址')),
                ('user_id', models.BigIntegerField(blank=True, null=True, verbose_name='用户ID')),
                ('user_type', models.Char<PERSON>ield(blank=True, max_length=10, null=True, verbose_name='用户类型(user/admin)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('hash_value', models.CharField(max_length=64, verbose_name='SM3哈希值')),
                ('prev_hash', models.CharField(blank=True, max_length=64, null=True, verbose_name='前一条记录的哈希值')),
            ],
            options={
                'verbose_name': '系统日志',
                'verbose_name_plural': '系统日志',
                'db_table': 'system_log',
                'indexes': [models.Index(fields=['log_type'], name='system_log_log_typ_822019_idx'), models.Index(fields=['module'], name='system_log_module_e8c164_idx'), models.Index(fields=['action'], name='system_log_action_88453c_idx'), models.Index(fields=['user_id'], name='system_log_user_id_fcedc2_idx'), models.Index(fields=['created_at'], name='system_log_created_510d42_idx')],
            },
        ),
        migrations.CreateModel(
            name='SecurityLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('log_type', models.CharField(max_length=20, verbose_name='日志类型(login_attempt/auth_failure/token_issue/token_revoke/password_change)')),
                ('user_id', models.BigIntegerField(blank=True, null=True, verbose_name='用户ID')),
                ('user_type', models.CharField(blank=True, max_length=10, null=True, verbose_name='用户类型(user/admin)')),
                ('ip_address', models.CharField(max_length=45, verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('description', models.TextField(verbose_name='日志描述')),
                ('status', models.CharField(default='success', max_length=20, verbose_name='状态(success/failed)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('hash_value', models.CharField(max_length=64, verbose_name='SM3哈希值')),
                ('prev_hash', models.CharField(blank=True, max_length=64, null=True, verbose_name='前一条记录的哈希值')),
            ],
            options={
                'verbose_name': '安全日志',
                'verbose_name_plural': '安全日志',
                'db_table': 'security_log',
                'indexes': [models.Index(fields=['log_type'], name='security_lo_log_typ_0627a1_idx'), models.Index(fields=['user_id'], name='security_lo_user_id_e9a024_idx'), models.Index(fields=['status'], name='security_lo_status_a12a79_idx'), models.Index(fields=['created_at'], name='security_lo_created_e93f72_idx')],
            },
        ),
        migrations.CreateModel(
            name='ApiRequestLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('request_id', models.CharField(max_length=64, unique=True, verbose_name='请求ID')),
                ('user_id', models.BigIntegerField(blank=True, null=True, verbose_name='用户ID')),
                ('user_type', models.CharField(blank=True, max_length=10, null=True, verbose_name='用户类型(user/admin)')),
                ('method', models.CharField(max_length=10, verbose_name='HTTP方法')),
                ('path', models.CharField(max_length=255, verbose_name='请求路径')),
                ('query_params', models.TextField(blank=True, null=True, verbose_name='查询参数')),
                ('request_body', models.TextField(blank=True, null=True, verbose_name='请求体')),
                ('response_status', models.IntegerField(verbose_name='响应状态码')),
                ('response_time', models.IntegerField(verbose_name='响应时间(毫秒)')),
                ('ip_address', models.CharField(max_length=45, verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('hash_value', models.CharField(max_length=64, verbose_name='SM3哈希值')),
                ('prev_hash', models.CharField(blank=True, max_length=64, null=True, verbose_name='前一条记录的哈希值')),
            ],
            options={
                'verbose_name': 'API请求日志',
                'verbose_name_plural': 'API请求日志',
                'db_table': 'api_request_log',
                'indexes': [models.Index(fields=['request_id'], name='api_request_request_36a426_idx'), models.Index(fields=['user_id'], name='api_request_user_id_cbf24e_idx'), models.Index(fields=['method'], name='api_request_method_8b5f63_idx'), models.Index(fields=['path'], name='api_request_path_eabdc1_idx'), models.Index(fields=['response_status'], name='api_request_respons_2e817f_idx'), models.Index(fields=['created_at'], name='api_request_created_0cd382_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserActionLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('action', models.CharField(max_length=50, verbose_name='操作名称')),
                ('description', models.TextField(verbose_name='操作描述')),
                ('ip_address', models.CharField(max_length=45, verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('target_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='目标类型')),
                ('target_id', models.BigIntegerField(blank=True, null=True, verbose_name='目标ID')),
                ('status', models.CharField(default='success', max_length=20, verbose_name='状态(success/failed)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('hash_value', models.CharField(max_length=64, verbose_name='SM3哈希值')),
                ('prev_hash', models.CharField(blank=True, max_length=64, null=True, verbose_name='前一条记录的哈希值')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户操作日志',
                'verbose_name_plural': '用户操作日志',
                'db_table': 'user_action_log',
                'indexes': [models.Index(fields=['user'], name='user_action_user_id_155554_idx'), models.Index(fields=['action'], name='user_action_action_f16a27_idx'), models.Index(fields=['status'], name='user_action_status_877189_idx'), models.Index(fields=['created_at'], name='user_action_created_108070_idx')],
            },
        ),
    ]
