<template>
  <div class="help-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>帮助中心</h2>
        </div>
      </template>

      <el-collapse v-model="activeNames">
        <el-collapse-item title="如何预约座位？" name="1">
          <div class="help-content">
            <p>预约座位的步骤如下：</p>
            <ol>
              <li>在侧边栏菜单中点击"座位管理" -> "预约座位"</li>
              <li>选择您想要预约的自习室</li>
              <li>在座位地图上选择一个可用的座位（绿色表示可用）</li>
              <li>选择预约的时间段</li>
              <li>点击"确认预约"按钮</li>
              <li>
                预约成功后，您可以在"个人中心" -> "我的预约"中查看预约详情
              </li>
            </ol>
          </div>
        </el-collapse-item>

        <el-collapse-item title="如何取消预约？" name="2">
          <div class="help-content">
            <p>取消预约的步骤如下：</p>
            <ol>
              <li>在侧边栏菜单中点击"个人中心" -> "我的预约"</li>
              <li>找到您想要取消的预约记录</li>
              <li>点击"取消预约"按钮</li>
              <li>在弹出的确认对话框中点击"确认"</li>
            </ol>
            <p class="warning">
              注意：预约开始前30分钟内取消预约将会扣除信誉分，请提前安排好您的时间。
            </p>
          </div>
        </el-collapse-item>

        <el-collapse-item title="如何签到和签退？" name="3">
          <div class="help-content">
            <p>签到和签退的步骤如下：</p>
            <ol>
              <li>在侧边栏菜单中点击"个人中心" -> "我的预约"</li>
              <li>找到当前正在进行的预约记录</li>
              <li>点击"签到"或"签退"按钮</li>
              <li>使用手机扫描生成的二维码完成操作</li>
            </ol>
            <p class="warning">
              注意：预约开始后15分钟内未签到将视为爽约，会扣除信誉分。
            </p>
          </div>
        </el-collapse-item>

        <el-collapse-item title="信誉分是什么？" name="4">
          <div class="help-content">
            <p>信誉分是衡量用户使用自习室行为的指标：</p>
            <ul>
              <li>新用户初始信誉分为100分</li>
              <li>按时签到签退、正常使用座位会维持或提高信誉分</li>
              <li>爽约、迟到、提前离开等行为会扣除信誉分</li>
              <li>信誉分低于60分将限制预约功能</li>
              <li>信誉分低于30分将暂停使用系统的权限</li>
            </ul>
            <p>
              您可以在"个人中心" -> "信誉分记录"中查看详细的信誉分变动记录。
            </p>
          </div>
        </el-collapse-item>

        <el-collapse-item title="联系管理员" name="5">
          <div class="help-content">
            <p>如果您在使用过程中遇到任何问题，可以通过以下方式联系管理员：</p>
            <ul>
              <li>电子邮件：<EMAIL></li>
              <li>电话：123-4567-8910</li>
              <li>前台服务台：图书馆一楼大厅</li>
            </ul>
            <p>服务时间：周一至周日 8:00-22:00</p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script>
import { ref } from "vue";

export default {
  name: "HelpView",
  setup() {
    const activeNames = ref(["1"]);

    return {
      activeNames,
    };
  },
};
</script>

<style lang="scss" scoped>
.help-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-content {
  padding: 10px;
  line-height: 1.6;

  ol,
  ul {
    padding-left: 20px;
    margin: 10px 0;
  }

  .warning {
    color: #e6a23c;
    font-weight: bold;
    margin: 10px 0;
  }
}
</style>
