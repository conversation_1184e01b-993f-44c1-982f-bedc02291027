# 图书馆自习室管理系统 - 接口方案设计补充

## 一、前后端数据交互规范

### 1.1 数据交互流程

为确保前后端数据交互的安全性和一致性，规范化以下数据交互流程：

```
┌─────────────┐                                      ┌─────────────┐
│             │                                      │             │
│    前端     │                                      │    后端     │
│             │                                      │             │
└──────┬──────┘                                      └──────┬──────┘
       │                                                    │
       │  1. 获取服务器SM2公钥                              │
       │ ─────────────────────────────────────────────────► │
       │                                                    │
       │  2. 返回服务器SM2公钥                              │
       │ ◄───────────────────────────────────────────────── │
       │                                                    │
       │  3. 生成随机SM4密钥                                │
       │     加密业务数据                                   │
       │     使用服务器公钥加密SM4密钥                      │
       │                                                    │
       │  4. 发送加密数据包                                 │
       │ ─────────────────────────────────────────────────► │
       │                                                    │
       │                                 5. 使用SM2私钥解密 │
       │                                    获取SM4密钥     │
       │                                    解密业务数据    │
       │                                    处理业务逻辑    │
       │                                                    │
       │  6. 返回加密响应                                   │
       │ ◄───────────────────────────────────────────────── │
       │                                                    │
       │  7. 解密响应数据                                   │
       │     更新界面                                       │
       │                                                    │
```

### 1.2 请求与响应格式规范

#### 1.2.1 普通请求格式

对于不需要加密的非敏感数据请求：

```json
{
  "param1": "value1",
  "param2": "value2",
  "timestamp": 1638320000000,
  "nonce": "随机字符串",
  "signature": "请求签名"
}
```

#### 1.2.2 加密请求格式

对于需要加密的敏感数据请求：

```json
{
  "encryptedData": "Base64编码的SM4加密数据",
  "encryptedKey": "Base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串",
  "signature": "请求签名(可选)"
}
```

#### 1.2.3 响应格式

统一的响应格式：

```json
{
  "code": 0,           // 状态码，0表示成功，非0表示错误
  "message": "success", // 状态描述
  "data": {            // 响应数据
    // 普通数据直接返回
    // 或者加密数据
    "encryptedData": "Base64编码的SM4加密数据",
    "encryptedKey": "Base64编码的SM2加密SM4密钥"
  }
}
```

### 1.3 请求头规范

为确保前后端通信的安全性和可追踪性，规范以下HTTP请求头：

| 请求头 | 说明 | 示例 |
|-------|------|------|
| Content-Type | 内容类型 | application/json |
| Authorization | 认证令牌 | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |
| X-Request-ID | 请求唯一标识 | 550e8400-e29b-41d4-a716-446655440000 |
| X-Timestamp | 请求时间戳 | 1638320000000 |
| X-Nonce | 随机字符串 | a1b2c3d4e5f6 |
| X-Signature | 请求签名 | 9ef5c0b2e0c7e4a1d8f2e3a1c0b9e8d7 |

### 1.4 CORS配置

为支持前后端分离架构，后端需配置正确的CORS策略：

```python
# Django设置
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8080",
    "https://yourdomain.com",
]

CORS_ALLOW_METHODS = [
    'GET',
    'POST',
    'PUT',
    'PATCH',
    'DELETE',
    'OPTIONS',
]

CORS_ALLOW_HEADERS = [
    'Authorization',
    'Content-Type',
    'X-Request-ID',
    'X-Timestamp',
    'X-Nonce',
    'X-Signature',
]
```

## 二、核心接口详细设计

### 2.1 用户认证接口

#### 2.1.1 获取服务器公钥

**接口**：GET /api/v1/crypto/server-public-key  
**功能**：获取服务器SM2公钥，用于加密传输  
**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "publicKey": "04开头的SM2公钥",
    "expires": 86400
  }
}
```

#### 2.1.2 用户注册

**接口**：POST /api/v1/auth/register  
**功能**：创建新用户账号  
**请求参数**：
```json
{
  "encryptedData": "Base64编码的SM4加密数据",
  "encryptedKey": "Base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串"
}
```

**加密前的原始数据**：
```json
{
  "student_id": "学号",
  "password": "SM3哈希的密码",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "public_key": "用户SM2公钥(可选)"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "user_id": 123,
    "student_id_hash": "SM3哈希的学号(部分显示)",
    "credit_score": 100
  }
}
```

#### 2.1.3 密码登录

**接口**：POST /api/v1/auth/login  
**功能**：用户密码登录获取令牌  
**请求参数**：
```json
{
  "encryptedData": "Base64编码的SM4加密数据",
  "encryptedKey": "Base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串"
}
```

**加密前的原始数据**：
```json
{
  "student_id": "学号",
  "password": "SM3哈希的密码"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": "JWT令牌",
    "expires_in": 3600,
    "user_info": {
      "user_id": 123,
      "student_id_hash": "SM3哈希的学号(部分显示)",
      "credit_score": 100
    }
  }
}
```

### 2.2 预约管理接口

#### 2.2.1 创建预约

**接口**：POST /api/v1/reservations  
**功能**：用户预约座位  
**请求参数**：
```json
{
  "encryptedData": "Base64编码的SM4加密数据",
  "encryptedKey": "Base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串"
}
```

**加密前的原始数据**：
```json
{
  "seat_id": 101,
  "start_time": "2023-12-01T08:00:00Z",
  "end_time": "2023-12-01T12:00:00Z",
  "remarks": "自习准备期末考试"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "预约成功",
  "data": {
    "id": 456,
    "seat_id": 101,
    "seat_number": "A-01",
    "room_id": 1,
    "room_name": "一号自习室",
    "start_time": "2023-12-01T08:00:00Z",
    "end_time": "2023-12-01T12:00:00Z",
    "status": "预约中",
    "created_at": "2023-11-25T10:30:00Z"
  }
}
```

## 三、前端实现指南

### 3.1 API请求封装

为统一处理加密请求，封装以下API请求函数：

```javascript
/**
 * API请求工具类
 */
const ApiService = {
  /**
   * 发送普通请求
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @param {Object} data - 请求数据
   * @returns {Promise<Object>} - 响应数据
   */
  async request(url, method, data = null) {
    const headers = {
      'Content-Type': 'application/json',
      'X-Request-ID': this.generateUUID(),
      'X-Timestamp': Date.now().toString(),
      'X-Nonce': this.generateNonce()
    };
    
    // 添加认证令牌
    const token = localStorage.getItem('token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const options = {
      method,
      headers,
      credentials: 'include'
    };
    
    if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`API错误: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  },
  
  /**
   * 发送加密请求
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @param {Object} data - 请求数据
   * @returns {Promise<Object>} - 响应数据
   */
  async encryptedRequest(url, method, data) {
    // 获取加密数据
    const encryptedData = await CryptoUtils.encryptRequest(data);
    
    // 发送请求
    return this.request(url, method, encryptedData);
  },
  
  /**
   * 生成UUID
   * @returns {string} - UUID
   */
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },
  
  /**
   * 生成随机nonce
   * @returns {string} - 随机字符串
   */
  generateNonce() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
};
```

### 3.2 使用示例

```javascript
// 用户注册示例
async function registerUser(userData) {
  try {
    const response = await ApiService.encryptedRequest(
      '/api/v1/auth/register',
      'POST',
      userData
    );
    
    if (response.code === 0) {
      // 注册成功处理
      return response.data;
    } else {
      // 处理错误
      throw new Error(response.message);
    }
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
}

// 创建预约示例
async function createReservation(reservationData) {
  try {
    const response = await ApiService.encryptedRequest(
      '/api/v1/reservations',
      'POST',
      reservationData
    );
    
    if (response.code === 0) {
      // 预约成功处理
      return response.data;
    } else {
      // 处理错误
      throw new Error(response.message);
    }
  } catch (error) {
    console.error('预约失败:', error);
    throw error;
  }
}
```

## 四、后端实现指南

### 4.1 加密请求处理

创建Django中间件或装饰器处理加密请求：

```python
class EncryptedRequestMiddleware:
    """处理加密请求的中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 检查是否为加密请求
        if self.is_encrypted_request(request):
            try:
                # 解密请求数据
                decrypted_data = self.decrypt_request(request)
                
                # 替换请求数据
                request.encrypted_original = request.data
                request._body = json.dumps(decrypted_data).encode('utf-8')
                request.data = decrypted_data
            except Exception as e:
                # 处理解密错误
                return JsonResponse({
                    'code': 400,
                    'message': f'请求解密失败: {str(e)}',
                    'data': None
                }, status=400)
        
        # 继续处理请求
        response = self.get_response(request)
        return response
    
    def is_encrypted_request(self, request):
        """检查是否为加密请求"""
        if not hasattr(request, 'data'):
            return False
        
        data = request.data
        return isinstance(data, dict) and 'encryptedData' in data and 'encryptedKey' in data
    
    def decrypt_request(self, request):
        """解密请求数据"""
        return CryptoUtils.decrypt_request_data(request.data)
```

### 4.2 API视图示例

```python
class UserRegistrationView(APIView):
    """用户注册视图"""
    
    def post(self, request):
        try:
            # 请求数据已在中间件中解密
            student_id = request.data.get('student_id')
            password = request.data.get('password')
            email = request.data.get('email')
            phone = request.data.get('phone')
            public_key = request.data.get('public_key')
            
            # 验证数据
            if not student_id or not password:
                return Response({
                    'code': 400,
                    'message': '学号和密码不能为空',
                    'data': None
                }, status=400)
            
            # 检查学号是否已存在
            student_id_hash = sm3_hash(student_id)
            if User.objects.filter(student_id_hash=student_id_hash).exists():
                return Response({
                    'code': 400,
                    'message': '该学号已注册',
                    'data': None
                }, status=400)
            
            # 创建用户
            user = User.objects.create(
                student_id=encrypt_with_sm4(student_id),
                student_id_hash=student_id_hash,
                password=password,  # 已经是SM3哈希
                email=encrypt_with_sm4(email) if email else None,
                phone=encrypt_with_sm4(phone) if phone else None,
                public_key=public_key,
                credit_score=100,
                status='active'
            )
            
            # 返回成功响应
            return Response({
                'code': 0,
                'message': '注册成功',
                'data': {
                    'user_id': user.id,
                    'student_id_hash': mask_hash(student_id_hash),
                    'credit_score': user.credit_score
                }
            }, status=201)
            
        except Exception as e:
            logger.error(f"用户注册失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'注册失败: {str(e)}',
                'data': None
            }, status=500)
```

## 五、安全最佳实践

### 5.1 前端安全措施

1. **不存储敏感数据**：
   - 不在本地存储明文敏感信息
   - 使用会话存储临时数据

2. **防止XSS攻击**：
   - 使用Vue的模板系统自动转义内容
   - 对用户输入进行验证和过滤

3. **防止CSRF攻击**：
   - 在请求中包含随机nonce
   - 验证请求来源

4. **安全的密钥管理**：
   - 不硬编码密钥
   - 动态获取服务器公钥

### 5.2 后端安全措施

1. **请求验证**：
   - 验证时间戳防止重放攻击
   - 验证nonce确保请求唯一性

2. **密钥保护**：
   - 服务器私钥安全存储
   - 定期轮换密钥

3. **日志记录**：
   - 记录所有API访问
   - 记录解密失败的请求

4. **异常处理**：
   - 捕获并记录所有异常
   - 返回适当的错误信息，不泄露系统细节
