"""
日志工具类
提供记录各种类型日志的功能，并确保日志的完整性和不可篡改性
"""
import json
import uuid
import logging
import time
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from utils.crypto import SM3Hasher
from .models import SystemLog, UserActionLog, SecurityLog, ApiRequestLog

logger = logging.getLogger(__name__)

def get_client_ip(request):
    """
    获取客户端IP地址
    
    参数:
        request: HTTP请求对象
        
    返回:
        IP地址字符串
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '')
    return ip


def get_user_agent(request):
    """
    获取用户代理字符串
    
    参数:
        request: HTTP请求对象
        
    返回:
        用户代理字符串
    """
    return request.META.get('HTTP_USER_AGENT', '')


def _calculate_hash(data_dict, prev_hash=None):
    """
    计算日志哈希值
    
    参数:
        data_dict: 日志数据字典
        prev_hash: 前一条日志的哈希值
        
    返回:
        哈希值(十六进制字符串)
    """
    # 复制数据字典，移除不需要计算哈希的字段
    hash_data = data_dict.copy()
    hash_data.pop('hash_value', None)
    hash_data.pop('prev_hash', None)
    
    # 将字典转换为有序的JSON字符串
    json_str = json.dumps(hash_data, sort_keys=True)
    
    # 如果有前一条记录的哈希值，添加到计算中
    if prev_hash:
        json_str = prev_hash + json_str
    
    # 计算SM3哈希
    return SM3Hasher.hash(json_str)


def add_system_log(log_type, module, action, description, user_id=None, user_type=None, ip_address=None):
    """
    添加系统日志
    
    参数:
        log_type: 日志类型(info/warning/error/security)
        module: 模块名称
        action: 操作名称
        description: 日志描述
        user_id: 用户ID
        user_type: 用户类型(user/admin)
        ip_address: IP地址
        
    返回:
        创建的日志对象
    """
    try:
        # 获取最后一条日志记录的哈希值
        prev_log = SystemLog.objects.order_by('-id').first()
        prev_hash = prev_log.hash_value if prev_log else None
        
        # 准备日志数据
        log_data = {
            'log_type': log_type,
            'module': module,
            'action': action,
            'description': description,
            'user_id': user_id,
            'user_type': user_type,
            'ip_address': ip_address,
            'created_at': timezone.now(),
        }
        
        # 计算哈希值
        hash_value = _calculate_hash(log_data, prev_hash)
        
        # 创建日志记录
        log = SystemLog.objects.create(
            log_type=log_type,
            module=module,
            action=action,
            description=description,
            user_id=user_id,
            user_type=user_type,
            ip_address=ip_address,
            hash_value=hash_value,
            prev_hash=prev_hash
        )
        
        return log
    except Exception as e:
        logger.error(f"添加系统日志失败: {str(e)}")
        # 如果记录日志失败，至少在控制台输出错误信息
        print(f"添加系统日志失败: {str(e)}")
        return None


def add_user_action_log(user, action, description, ip_address, user_agent, 
                       target_type=None, target_id=None, status='success'):
    """
    添加用户操作日志
    
    参数:
        user: 用户对象
        action: 操作名称
        description: 操作描述
        ip_address: IP地址
        user_agent: 用户代理
        target_type: 目标类型
        target_id: 目标ID
        status: 状态(success/failed)
        
    返回:
        创建的日志对象
    """
    try:
        # 获取最后一条日志记录的哈希值
        prev_log = UserActionLog.objects.order_by('-id').first()
        prev_hash = prev_log.hash_value if prev_log else None
        
        # 准备日志数据
        log_data = {
            'user_id': user.id,
            'action': action,
            'description': description,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'target_type': target_type,
            'target_id': target_id,
            'status': status,
            'created_at': timezone.now(),
        }
        
        # 计算哈希值
        hash_value = _calculate_hash(log_data, prev_hash)
        
        # 创建日志记录
        log = UserActionLog.objects.create(
            user=user,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            target_type=target_type,
            target_id=target_id,
            status=status,
            hash_value=hash_value,
            prev_hash=prev_hash
        )
        
        return log
    except Exception as e:
        logger.error(f"添加用户操作日志失败: {str(e)}")
        return None


def add_security_log(log_type, description, ip_address, user_agent, 
                    user_id=None, user_type=None, status='success'):
    """
    添加安全日志
    
    参数:
        log_type: 日志类型(login_attempt/auth_failure/token_issue/token_revoke/password_change)
        description: 日志描述
        ip_address: IP地址
        user_agent: 用户代理
        user_id: 用户ID
        user_type: 用户类型(user/admin)
        status: 状态(success/failed)
        
    返回:
        创建的日志对象
    """
    try:
        # 获取最后一条日志记录的哈希值
        prev_log = SecurityLog.objects.order_by('-id').first()
        prev_hash = prev_log.hash_value if prev_log else None
        
        # 准备日志数据
        log_data = {
            'log_type': log_type,
            'description': description,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'user_id': user_id,
            'user_type': user_type,
            'status': status,
            'created_at': timezone.now(),
        }
        
        # 计算哈希值
        hash_value = _calculate_hash(log_data, prev_hash)
        
        # 创建日志记录
        log = SecurityLog.objects.create(
            log_type=log_type,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            user_id=user_id,
            user_type=user_type,
            status=status,
            hash_value=hash_value,
            prev_hash=prev_hash
        )
        
        return log
    except Exception as e:
        logger.error(f"添加安全日志失败: {str(e)}")
        return None


def add_api_request_log(request, response, response_time):
    """
    添加API请求日志
    
    参数:
        request: HTTP请求对象
        response: HTTP响应对象
        response_time: 响应时间(毫秒)
        
    返回:
        创建的日志对象
    """
    try:
        # 获取最后一条日志记录的哈希值
        prev_log = ApiRequestLog.objects.order_by('-id').first()
        prev_hash = prev_log.hash_value if prev_log else None
        
        # 获取用户ID和类型
        user_id = None
        user_type = None
        if hasattr(request, 'user') and request.user.is_authenticated:
            user_id = request.user.id
            user_type = 'admin' if request.user.is_staff else 'user'
        
        # 获取请求体(如果是JSON)
        request_body = None
        if request.content_type == 'application/json' and request.body:
            try:
                request_body = json.loads(request.body)
                # 敏感字段脱敏
                if isinstance(request_body, dict):
                    if 'password' in request_body:
                        request_body['password'] = '******'
                    if 'token' in request_body:
                        request_body['token'] = '******'
                request_body = json.dumps(request_body)
            except:
                request_body = None
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 准备日志数据
        log_data = {
            'request_id': request_id,
            'user_id': user_id,
            'user_type': user_type,
            'method': request.method,
            'path': request.path,
            'query_params': json.dumps(dict(request.GET)) if request.GET else None,
            'request_body': request_body,
            'response_status': response.status_code,
            'response_time': response_time,
            'ip_address': get_client_ip(request),
            'user_agent': get_user_agent(request),
            'created_at': timezone.now(),
        }
        
        # 计算哈希值
        hash_value = _calculate_hash(log_data, prev_hash)
        
        # 创建日志记录
        log = ApiRequestLog.objects.create(
            request_id=request_id,
            user_id=user_id,
            user_type=user_type,
            method=request.method,
            path=request.path,
            query_params=log_data['query_params'],
            request_body=request_body,
            response_status=response.status_code,
            response_time=response_time,
            ip_address=log_data['ip_address'],
            user_agent=log_data['user_agent'],
            hash_value=hash_value,
            prev_hash=prev_hash
        )
        
        return log
    except Exception as e:
        logger.error(f"添加API请求日志失败: {str(e)}")
        return None


def verify_log_chain(log_type, start_id=None, end_id=None):
    """
    验证日志哈希链的完整性
    
    参数:
        log_type: 日志类型(system/user_action/security/api_request)
        start_id: 起始日志ID
        end_id: 结束日志ID
        
    返回:
        (是否完整, 问题日志ID列表)
    """
    try:
        # 根据日志类型选择模型
        if log_type == 'system':
            model = SystemLog
        elif log_type == 'user_action':
            model = UserActionLog
        elif log_type == 'security':
            model = SecurityLog
        elif log_type == 'api_request':
            model = ApiRequestLog
        else:
            raise ValueError(f"不支持的日志类型: {log_type}")
        
        # 构建查询条件
        query = {}
        if start_id is not None:
            query['id__gte'] = start_id
        if end_id is not None:
            query['id__lte'] = end_id
        
        # 获取日志记录
        logs = list(model.objects.filter(**query).order_by('id'))
        
        if not logs:
            return True, []
        
        # 验证哈希链
        problematic_ids = []
        for i in range(len(logs)):
            current = logs[i]
            
            # 如果不是第一条记录，验证prev_hash
            if i > 0:
                previous = logs[i-1]
                if current.prev_hash != previous.hash_value:
                    problematic_ids.append(current.id)
                    continue
            
            # 重新计算当前记录的哈希值
            log_data = {
                field.name: getattr(current, field.name)
                for field in current._meta.fields
                if field.name not in ['hash_value', 'prev_hash']
            }
            
            calculated_hash = _calculate_hash(log_data, current.prev_hash)
            
            # 验证哈希值是否一致
            if calculated_hash != current.hash_value:
                problematic_ids.append(current.id)
        
        return len(problematic_ids) == 0, problematic_ids
    
    except Exception as e:
        logger.error(f"验证日志链失败: {str(e)}")
        return False, []
