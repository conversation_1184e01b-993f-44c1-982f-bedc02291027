"""
用户认证工具函数
"""
import logging
import binascii
from datetime import timedelta
from django.utils import timezone
from django.conf import settings
from .models import User, UserSession, CreditRecord
from utils.crypto import SM3Hasher, SM4Crypto

logger = logging.getLogger(__name__)

def get_client_ip(request):
    """
    获取客户端IP地址
    
    参数:
        request: 请求对象
        
    返回:
        IP地址字符串
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def decrypt_user_data(user):
    """
    解密用户敏感数据
    
    参数:
        user: 用户对象
        
    返回:
        包含解密数据的字典
    """
    result = {
        'student_id': None,
        'email': None,
        'phone': None
    }
    
    # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
    sm4_key = SM4Crypto.generate_key()
    
    # 解密学号
    if user.student_id:
        try:
            encrypted_student_id = binascii.hexlify(user.student_id).decode()
            result['student_id'] = SM4Crypto.decrypt(sm4_key, encrypted_student_id)
        except Exception as e:
            logger.error(f"解密学号失败: {str(e)}")
    
    # 解密邮箱
    if user.email:
        try:
            encrypted_email = binascii.hexlify(user.email).decode()
            result['email'] = SM4Crypto.decrypt(sm4_key, encrypted_email)
        except Exception as e:
            logger.error(f"解密邮箱失败: {str(e)}")
    
    # 解密手机号
    if user.phone:
        try:
            encrypted_phone = binascii.hexlify(user.phone).decode()
            result['phone'] = SM4Crypto.decrypt(sm4_key, encrypted_phone)
        except Exception as e:
            logger.error(f"解密手机号失败: {str(e)}")
    
    return result


def encrypt_user_data(data):
    """
    加密用户敏感数据
    
    参数:
        data: 包含明文数据的字典
        
    返回:
        包含加密数据的字典
    """
    result = {}
    
    # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
    sm4_key = SM4Crypto.generate_key()
    
    # 加密学号
    if 'student_id' in data and data['student_id']:
        try:
            encrypted_student_id = SM4Crypto.encrypt(sm4_key, data['student_id'])
            result['student_id'] = binascii.unhexlify(encrypted_student_id)
            result['student_id_hash'] = SM3Hasher.hash(data['student_id'])
        except Exception as e:
            logger.error(f"加密学号失败: {str(e)}")
    
    # 加密邮箱
    if 'email' in data and data['email']:
        try:
            encrypted_email = SM4Crypto.encrypt(sm4_key, data['email'])
            result['email'] = binascii.unhexlify(encrypted_email)
        except Exception as e:
            logger.error(f"加密邮箱失败: {str(e)}")
    
    # 加密手机号
    if 'phone' in data and data['phone']:
        try:
            encrypted_phone = SM4Crypto.encrypt(sm4_key, data['phone'])
            result['phone'] = binascii.unhexlify(encrypted_phone)
        except Exception as e:
            logger.error(f"加密手机号失败: {str(e)}")
    
    return result


def update_credit_score(user, delta, reason, operator_type='system', operator_id=None, related_entity=None, related_id=None):
    """
    更新用户信誉分
    
    参数:
        user: 用户对象
        delta: 变动分值
        reason: 变动原因
        operator_type: 操作员类型
        operator_id: 操作员ID
        related_entity: 相关实体
        related_id: 相关实体ID
        
    返回:
        更新后的信誉分
    """
    # 计算新的信誉分
    new_score = user.credit_score + delta
    
    # 限制信誉分范围
    if new_score < 0:
        new_score = 0
    elif new_score > 100:
        new_score = 100
    
    # 更新用户信誉分
    user.credit_score = new_score
    user.save(update_fields=['credit_score'])
    
    # 记录信誉分变动
    CreditRecord.objects.create(
        user=user,
        delta=delta,
        reason=reason,
        score_after=new_score,
        operator_type=operator_type,
        operator_id=operator_id,
        related_entity=related_entity,
        related_id=related_id
    )
    
    # 检查信誉分是否低于阈值
    if new_score < 60 and user.status == 'active':
        user.status = 'blacklisted'
        user.save(update_fields=['status'])
        
        # 记录黑名单
        from apps.seat.models import BlacklistRecord
        BlacklistRecord.objects.create(
            user=user,
            reason=f"信誉分低于阈值: {new_score}",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(days=7),
            status='active',
            operator_type='system'
        )
        
        logger.warning(f"用户 {user.student_id_hash} 因信誉分过低被加入黑名单")
    
    return new_score


def clean_expired_sessions():
    """
    清理过期的会话
    
    返回:
        清理的会话数量
    """
    now = timezone.now()
    expired_sessions = UserSession.objects.filter(
        is_active=True,
        expires_at__lt=now
    )
    
    count = expired_sessions.count()
    if count > 0:
        expired_sessions.update(is_active=False)
        logger.info(f"清理了 {count} 个过期会话")
    
    return count


def unlock_accounts():
    """
    解锁被锁定的账号
    
    返回:
        解锁的账号数量
    """
    # 获取锁定超过24小时的账号
    lock_threshold = timezone.now() - timedelta(hours=24)
    locked_users = User.objects.filter(
        status='locked',
        updated_at__lt=lock_threshold
    )
    
    count = locked_users.count()
    if count > 0:
        locked_users.update(
            status='active',
            login_attempts=0
        )
        logger.info(f"解锁了 {count} 个被锁定的账号")
    
    return count


def restore_credit_scores():
    """
    恢复用户信誉分
    
    返回:
        恢复信誉分的用户数量
    """
    # 获取信誉分低于100且最近7天没有负面记录的用户
    threshold_date = timezone.now() - timedelta(days=7)
    users_to_restore = User.objects.filter(
        credit_score__lt=100,
        status='active'
    ).exclude(
        creditrecord__delta__lt=0,
        creditrecord__created_at__gt=threshold_date
    )
    
    count = 0
    for user in users_to_restore:
        # 每周恢复5点信誉分
        update_credit_score(
            user=user,
            delta=5,
            reason="定期信誉分恢复",
            operator_type='system'
        )
        count += 1
    
    if count > 0:
        logger.info(f"恢复了 {count} 个用户的信誉分")
    
    return count
