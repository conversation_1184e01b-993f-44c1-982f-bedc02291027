<template>
  <footer class="app-footer">
    <div class="footer-content">
      <p>
        &copy; {{ currentYear }} 图书馆自习室管理系统 |
        基于国密算法的安全座位管理平台
      </p>
      <p>
        <a href="/about">关于我们</a>
        |
        <a href="/privacy">隐私政策</a>
        |
        <a href="/terms">使用条款</a>
        |
        <a href="/contact">联系我们</a>
      </p>
    </div>
  </footer>
</template>

<script>
import { computed } from "vue";

export default {
  name: "AppFooter",
  setup() {
    const currentYear = computed(() => new Date().getFullYear());

    return {
      currentYear,
    };
  },
};
</script>

<style lang="scss" scoped>
.app-footer {
  background-color: #f5f7fa;
  padding: 15px 0;
  text-align: center;
  font-size: 0.9rem;
  color: #606266;
  border-top: 1px solid #e6e6e6;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;

  p {
    margin: 5px 0;
  }

  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
