# 图书馆自习室管理系统 - 项目开发流程与修改说明

## 一、技术选型与环境配置

### 1.1 技术栈调整

- **数据库连接**：鉴于开发中遇到的依赖安装问题，使用pymysql替代mysqlclient作为MySQL数据库连接库
- **缓存方案**：优先使用Redis作为缓存，如环境不支持则降级使用Django本地缓存
- **前端框架**：使用Vue 3 + Element Plus构建用户界面，确保响应式设计
- **国密算法库**：
  - 后端：gmssl库（Python版）
  - 前端：sm-crypto库（JavaScript版）

### 1.2 开发环境配置

- **后端环境**：
  - Python 3.8+
  - Django 4.x + Django REST Framework
  - MySQL 8.x (使用pymysql连接)
  - Redis 6.x (可选)

- **前端环境**：
  - Node.js 16+
  - Vue 3
  - Element Plus
  - sm-crypto

## 二、项目开发流程

### 2.1 开发阶段划分

1. **基础架构搭建**（1周）
   - 后端Django项目初始化
   - 数据库设计与迁移
   - 前端Vue项目搭建
   - 国密算法环境配置

2. **核心安全模块开发**（2周）
   - 国密算法工具类实现
   - 混合加密传输方案实现
   - 身份认证模块开发
   - 日志安全模块开发

3. **业务功能开发**（3周）
   - 用户管理功能
   - 自习室与座位管理
   - 预约系统实现
   - 签到功能实现

4. **前端界面开发**（2周）
   - 用户界面开发
   - 座位地图可视化
   - 预约流程界面
   - 管理员界面

5. **测试与优化**（1周）
   - 功能测试
   - 安全测试
   - 性能优化
   - 问题修复

6. **部署与上线**（1周）
   - 环境配置
   - 数据迁移
   - 上线测试
   - 监控配置

### 2.2 迭代开发策略

采用敏捷开发方法，按照以下优先级进行迭代：

1. **第一迭代**：核心安全基础设施 + 用户认证
2. **第二迭代**：自习室和座位管理 + 预约功能
3. **第三迭代**：签到功能 + 日志系统
4. **第四迭代**：管理功能 + 统计分析
5. **第五迭代**：UI优化 + 性能调优

## 三、前后端功能划分

### 3.1 后端职责

1. **数据管理**
   - 数据库设计与维护
   - 数据加密存储
   - 数据完整性保障
   - 数据备份与恢复

2. **业务逻辑**
   - 用户认证与授权
   - 预约冲突检测
   - 座位分配算法
   - 签到验证逻辑

3. **安全保障**
   - SM2/SM3/SM4算法实现
   - 密钥管理与轮换
   - 日志哈希链维护
   - 安全审计与告警

4. **API服务**
   - RESTful API设计
   - WebSocket实时通信
   - 接口加密与签名
   - 接口权限控制

### 3.2 前端职责

1. **用户界面**
   - 响应式页面设计
   - 座位地图可视化
   - 表单验证与提交
   - 状态管理与更新

2. **安全处理**
   - SM2/SM4前端加密实现
   - 敏感数据加密传输
   - 令牌管理与刷新
   - 防XSS与CSRF保护

3. **用户体验**
   - 交互设计优化
   - 加载状态与反馈
   - 错误处理与提示
   - 操作引导与帮助

4. **本地存储**
   - 用户配置存储
   - 缓存优化
   - 离线功能支持
   - 本地数据安全

## 四、混合加密方案详解

### 4.1 混合加密传输方案与SM2证书认证的区别

#### 4.1.1 混合加密传输方案中的公钥私钥

**密钥所有者**：
- 公钥：服务器的公钥（固定的系统公钥）
- 私钥：服务器的私钥（固定的系统私钥）

**密钥用途**：
- 公钥：客户端使用服务器公钥加密SM4对称密钥
- 私钥：服务器使用自己的私钥解密SM4对称密钥

**密钥管理**：
- 服务器公钥对所有用户公开，存储在前端配置中
- 服务器私钥严格保密，只存储在服务器端

**特点**：
- 所有用户使用同一个服务器公钥加密数据
- 服务器使用唯一的私钥解密所有用户的数据
- 密钥对是系统级别的，与具体用户无关

#### 4.1.2 SM2证书认证中的公钥私钥

**密钥所有者**：
- 公钥：用户的公钥（每个用户不同）
- 私钥：用户的私钥（每个用户不同）

**密钥用途**：
- 公钥：服务器使用用户公钥验证用户签名
- 私钥：用户使用自己的私钥对挑战字符串进行签名

**密钥管理**：
- 用户公钥在注册时提交给服务器，存储在用户数据库中
- 用户私钥由用户自己保管，不上传给服务器

**特点**：
- 每个用户有自己的SM2密钥对
- 用户私钥只有用户自己知道，不上传给服务器
- 服务器存储所有用户的公钥
- 密钥对是用户级别的，与具体用户绑定

#### 4.1.3 关键区别总结

**密钥所有权不同**：
- 混合加密传输：使用的是服务器的密钥对
- SM2证书认证：使用的是用户的密钥对

**密钥存储位置不同**：
- 混合加密传输：
  - 服务器公钥：存储在前端配置中
  - 服务器私钥：存储在服务器配置中
- SM2证书认证：
  - 用户公钥：存储在服务器数据库中
  - 用户私钥：由用户自己保管，不上传给服务器

**密钥使用方式不同**：
- 混合加密传输：公钥用于加密，私钥用于解密
- SM2证书认证：私钥用于签名，公钥用于验证签名

**密钥数量不同**：
- 混合加密传输：整个系统只有一对密钥
- SM2证书认证：每个用户都有一对独立的密钥

**密钥安全责任不同**：
- 混合加密传输：服务器负责保护私钥安全
- SM2证书认证：用户负责保护自己的私钥安全

### 4.2 在系统中的配合使用

在完整的安全系统中，这两种密钥机制同时存在：

**系统级密钥对**：用于混合加密传输，保护所有数据传输
- 服务器公钥：CRYPTO_CONFIG.SERVER_PUBLIC_KEY
- 服务器私钥：存储在服务器的SM2_PRIVATE_KEY_PATH

**用户级密钥对**：用于证书认证，验证用户身份
- 用户公钥：存储在User模型的public_key字段
- 用户私钥：由用户自己保管

这种双重密钥机制设计提供了全面的安全保障：既保护了数据传输安全，又提供了可靠的身份验证机制，是国密算法在系统中的综合应用。

## 五、前后端编码转换规范

### 5.1 数据编码标准

为解决前后端加解密过程中的编码不一致问题，统一采用以下编码标准：

1. **字符串编码**：统一使用UTF-8编码
2. **二进制数据表示**：使用Base64编码传输
3. **十六进制字符串**：小写形式，不带0x前缀
4. **密钥格式**：
   - SM2公钥：使用压缩格式（带04前缀的十六进制字符串）
   - SM4密钥：16字节（32位十六进制字符串）

### 5.2 SM4加密参数统一

为确保前后端SM4加密解密兼容，统一以下参数：

1. **工作模式**：CBC模式
2. **填充方式**：PKCS#7填充
3. **初始向量(IV)**：16字节随机生成
4. **IV传输**：IV与密文拼接（IV在前，密文在后）
5. **结果编码**：Base64编码

### 5.3 SM2加密模式统一

为解决前后端SM2加密解密不兼容问题：

1. **加密模式**：统一使用C1C3C2模式
2. **公钥格式**：统一使用非压缩格式（带04前缀）
3. **结果编码**：Base64编码

### 5.4 数据传输格式规范

#### 5.4.1 请求数据格式

```json
{
  "encryptedData": "Base64编码的SM4加密数据",
  "encryptedKey": "Base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串"
}
```

#### 5.4.2 SM4加密数据结构

```
[16字节IV][加密后的数据]
```

#### 5.4.3 前端加密示例

```javascript
// 生成随机SM4密钥(16字节)
const sm4Key = generateRandomHex(32); // 返回32位十六进制字符串

// 生成随机IV(16字节)
const iv = generateRandomHex(32); // 返回32位十六进制字符串

// 使用SM4-CBC模式加密数据
const encryptedData = sm4.encrypt(JSON.stringify(data), sm4Key, {
  mode: 'cbc',
  iv: iv,
  padding: 'pkcs#7'
});

// 拼接IV和密文，并Base64编码
const ivAndEncryptedData = iv + encryptedData;
const base64EncryptedData = btoa(hexToBytes(ivAndEncryptedData));

// 使用服务器SM2公钥加密SM4密钥
const encryptedKey = sm2.doEncrypt(sm4Key, serverPublicKey, 1); // 使用C1C2C3模式
const base64EncryptedKey = btoa(encryptedKey);

// 构建最终请求数据
const requestData = {
  encryptedData: base64EncryptedData,
  encryptedKey: base64EncryptedKey,
  timestamp: Date.now(),
  nonce: generateNonce()
};
```

#### 5.4.4 后端解密示例

```python
# 解析请求数据
encrypted_data_base64 = request.data.get('encryptedData')
encrypted_key_base64 = request.data.get('encryptedKey')

# Base64解码
encrypted_data_hex = base64.b64decode(encrypted_data_base64).hex()
encrypted_key_hex = base64.b64decode(encrypted_key_base64).hex()

# 提取IV(前32位十六进制字符，即16字节)
iv_hex = encrypted_data_hex[:32]
ciphertext_hex = encrypted_data_hex[32:]

# 使用SM2私钥解密SM4密钥
sm2_crypt = sm2.CryptSM2(
    public_key=None,
    private_key=settings.SM2_PRIVATE_KEY
)
sm4_key = sm2_crypt.decrypt(encrypted_key_hex)

# 使用SM4密钥解密数据
sm4_crypt = SM4()
sm4_crypt.set_key(bytes.fromhex(sm4_key), SM4.SM4_DECRYPT)
decrypted_data = sm4_crypt.crypt_cbc(
    bytes.fromhex(iv_hex),
    bytes.fromhex(ciphertext_hex)
)

# 去除填充并解析JSON
unpadder = padding.PKCS7(128).unpadder()
data = unpadder.update(decrypted_data) + unpadder.finalize()
json_data = json.loads(data.decode('utf-8'))
```

## 六、开发优先级与里程碑

### 6.1 核心功能优先级

按照以下优先级开发核心功能：

1. **用户认证与安全基础设施**
   - 国密算法工具类
   - 混合加密传输方案
   - 用户注册与登录

2. **自习室与座位管理**
   - 自习室数据模型
   - 座位状态管理
   - 座位地图可视化

3. **预约系统**
   - 预约创建与管理
   - 冲突检测
   - 预约状态更新

4. **签到功能**
   - 二维码生成
   - 签到验证
   - 座位状态更新

5. **管理功能**
   - 用户管理
   - 违规处理
   - 统计分析

### 6.2 项目里程碑

1. **里程碑1**：安全基础设施完成
   - 国密算法工具类实现
   - 混合加密方案测试通过
   - 用户认证功能可用

2. **里程碑2**：核心业务功能可用
   - 自习室和座位管理完成
   - 预约功能可用
   - 基础UI界面完成

3. **里程碑3**：完整功能测试版
   - 签到功能完成
   - 管理功能完成
   - 全流程功能测试通过

4. **里程碑4**：优化与上线准备
   - UI优化完成
   - 性能测试通过
   - 安全测试通过
   - 部署文档完成
六：修改：使用django后端自带admin实现管理员界面开发