二、课题的基本内容
本课题的研究内容包括：
一、研究国密算法在自习室管理系统中的关键技术
研究SM2、SM3、SM4算法在自习室管理场景下的核心应用，包括：SM2非对称加密实现用户身份认证、SM4对称加密保护预约记录与座位状态等敏感数据、SM3哈希算法保障操作日志的完整性。结合现有国密算法库，完成算法调用与参数配置,设计混合加密方案，确保系统符合密码应用安全标准。

二、图书馆自习室管理系统功能设计与实现
研究构建分层系统架构，开发核心功能模块：用户身份认证模块（支持扫码签到与动态令牌生成）、预约管理模块（时段冲突检测）、动态调度模块（基于先到先得策略的座位分配）。敏感数据采用国密算法进行端到端加密存储与传输，数据库字段级加密使用SM4算法，操作日志通过SM3哈希链防篡改，实现“认证-传输-存储”安全体系设计。

三、系统功能验证与安全性评估
通过黑盒测试验证用户登录、预约、签到等基础功能完整性；利用压力测试工具模拟50~100用户并发访问，评估系统响应时间与稳定性；结合渗透测试验证国密算法抗攻击能力（如中间人攻击、数据篡改等），检测加密字段解密失败率与日志篡改告警成功率，确保系统满足高校图书馆实际管理需求。


  三、课题研究或实施方案
经过调研文献以及个人考虑，本课题想法与实施方案如下：
一、国密算法关键技术实现
算法库选型与配置：选用国家密码管理局认证的gmssl库，其提供标准化的SM2/SM3/SM4接口，支持密钥生成、数据加密及哈希计算。
配置SM2密钥对：生成学生身份绑定的数字证书（学号作为证书标识），私钥由服务器安全存储，公钥加密后存储至数据库。
SM4动态密钥管理：每30分钟通过SM2密钥交换协议生成新会话密钥，确保前向安全性，避免密钥长期复用风险。
混合加密方案设计：
1.数据传输流程：前端生成随机SM4会话密钥，对业务数据（如预约记录、座位状态）进行加密；使用SM2公钥加密会话密钥，形成“信封加密”结构，避免纯对称加密的密钥分发风险；后端通过SM2私钥解密会话密钥，再用SM4解密业务数据，完成请求处理。
2.数据存储方案：
敏感字段（如学号、座位号、预约时段）采用SM4-CBC模式加密存储，初始向量与密钥分离管理；操作日志生成SM3哈希值，按时间顺序链接成哈希链，篡改任一记录将导致后续哈希值不匹配。
二、系统功能设计与实现
1.系统架构设计：


用户层：提供移动端（小程序）与Web端界面，支持可视化座位地图、预约操作及扫码签到；
业务层：基于Django框架构建REST API，处理身份认证、预约冲突检测、动态调度等逻辑；
数据层：采用MySQL数据库，敏感字段SM4加密存储，日志表通过SM3哈希链防篡改。

2.核心模块开发：

身份认证模块：
学号登录：前端提交学号与密码（SM3哈希加密），后端验证SM2数字证书；
扫码签到：动态生成含时间戳的二维码，后端通过SM3哈希校验合法性。
预约管理模块：
冲突检测：查询同一座位在选定时间内的预约记录，比对时间重叠情况，实时提示冲突情况；
数据加密传输：预约请求与分配结果通过SM4加密，前端解密后渲染座位状态。

动态调度模块：
先到先得策略：按预约提交时间顺序分配座位，空闲列表按给定时间重复更新；
异常处理：超时未签到自动释放座位，违规记录通过SM3哈希链追溯。

安全设计方面：
对数据库内敏感字段以二进制格式（BLOB）存储，读写时自动调用SM4加解密接口；同时记录用户操作与系统事件，生成SM3哈希链，支持篡改检测与告警。

三、系统验证与评估
功能测试：
黑盒测试：覆盖正常流程（登录→选座→签到）及异常场景（重复预约、超时占座），使用Postman模拟HTTP请求，验证接口返回状态码与数据一致性。
自动化测试：通过Selenium编写UI测试脚本，模拟用户操作，检测前端交互与数据渲染准确性。

安全测试：
抗攻击验证：
SQL注入：尝试通过恶意输入绕过登录或篡改数据，检测SM4加密字段的抗注入能力；
中间人攻击：使用Wireshark抓取网络流量，验证传输数据是否为SM4密文。
完整性校验：手动修改数据库日志哈希链字段，触发系统警告。

性能测试：通过JMeter模拟50~100用户并发访问，设定监控指标等。