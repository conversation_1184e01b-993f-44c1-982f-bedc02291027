图书馆自习室管理系统 - 接口设计方案

## 一、概述

### 1.1 设计目标

本接口设计方案旨在为图书馆自习室管理系统提供全面、安全、灵活的API接口，支持前后端分离架构，满足Web端和移动端的访问需求。接口设计遵循RESTful风格，同时融入国密算法安全机制，确保数据传输和交互的安全性。

### 1.2 设计原则

1. **标准化**：采用RESTful风格设计API，保持命名和使用方式的一致性
2. **安全性**：全面整合国密算法，保障接口安全
3. **易用性**：接口设计简洁明了，易于理解和使用
4. **可扩展性**：预留足够的扩展空间，支持未来功能演进
5. **性能优化**：合理设计接口粒度，避免过多请求和数据冗余

### 1.3 技术栈选择

1. **API框架**：Django REST Framework
2. **认证机制**：JWT(JSON Web Token) + 国密算法验证
3. **数据格式**：JSON
4. **通信安全**：HTTPS + 国密算法加密传输
5. **文档工具**：Swagger / OpenAPI

## 二、API设计规范

### 2.1 URI设计规范

1. **基础路径**：`/api/v1/`作为API的基础路径，便于版本管理
2. **资源命名**：使用复数名词，如`/api/v1/users`、`/api/v1/rooms`、`/api/v1/seats`
3. **下级资源**：使用子资源路径，如`/api/v1/rooms/{room_id}/seats`
4. **查询参数**：使用`?`传递过滤、排序、分页参数，如`/api/v1/seats?status=available&room_id=1`
5. **版本控制**：在URL中体现API版本，如`/api/v1/`、`/api/v2/`

### 2.2 HTTP方法使用规范

| HTTP方法 | 用途 | 示例 |
|---------|------|------|
| GET | 获取资源 | GET /api/v1/seats - 获取座位列表 |
| POST | 创建资源 | POST /api/v1/reservations - 创建预约 |
| PUT | 全量更新资源 | PUT /api/v1/users/{id} - 更新用户全部信息 |
| PATCH | 部分更新资源 | PATCH /api/v1/seats/{id} - 更新座位部分信息 |
| DELETE | 删除资源 | DELETE /api/v1/reservations/{id} - 取消预约 |

### 2.3 请求与响应格式

#### 2.3.1 请求格式

**请求头要求**：
- `Content-Type: application/json` - JSON数据格式
- `Authorization: Bearer {token}` - JWT认证令牌
- `X-Request-ID: {uuid}` - 请求唯一标识，用于跟踪和调试

**请求体示例**（JSON格式）：
```json
{
  "seat_id": 123,
  "start_time": "2023-12-01T08:00:00Z",
  "end_time": "2023-12-01T12:00:00Z",
  "remarks": "自习准备期末考试"
}
```

**加密请求体示例**：
```json
{
  "encryptedData": "base64编码的SM4加密数据",
  "encryptedKey": "base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串"
}
```

#### 2.3.2 响应格式

**响应状态码**：
- 200 OK - 请求成功
- 201 Created - 资源创建成功
- 400 Bad Request - 请求参数错误
- 401 Unauthorized - 未认证或认证失败
- 403 Forbidden - 权限不足
- 404 Not Found - 资源不存在
- 500 Internal Server Error - 服务器内部错误

**响应体示例**（JSON格式）：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 456,
    "seat_id": 123,
    "start_time": "2023-12-01T08:00:00Z",
    "end_time": "2023-12-01T12:00:00Z",
    "status": "预约中",
    "created_at": "2023-11-25T10:30:00Z"
  }
}
```

**错误响应示例**：
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": [
    {
      "field": "end_time",
      "message": "结束时间必须晚于开始时间"
    }
  ]
}
```

## 三、核心API接口设计

### 3.1 用户认证接口

#### 3.1.1 用户注册

**接口**：POST /api/v1/auth/register
**功能**：创建新用户账号
**请求参数**：
```json
{
  "student_id": "SM3哈希的学号",
  "password": "SM3哈希的密码",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "public_key": "用户SM2公钥(可选)"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "user_id": 123,
    "student_id_hash": "SM3哈希的学号(部分显示)",
    "credit_score": 100
  }
}
```

#### 3.1.2 密码登录

**接口**：POST /api/v1/auth/login
**功能**：用户密码登录获取令牌
**请求参数**：
```json
{
  "student_id": "学号",
  "password": "SM3哈希的密码"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": "JWT令牌",
    "expires_in": 3600,
    "user_info": {
      "user_id": 123,
      "student_id_hash": "SM3哈希的学号(部分显示)",
      "credit_score": 100
    }
  }
}
```

#### 3.1.3 证书登录

**接口**：POST /api/v1/auth/certificate_login
**功能**：用户SM2证书登录获取令牌
**请求参数**：
```json
{
  "student_id": "学号",
  "challenge": "服务器下发的挑战字符串",
  "signature": "SM2私钥对挑战字符串的签名"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": "JWT令牌",
    "expires_in": 3600,
    "user_info": {
      "user_id": 123,
      "student_id_hash": "SM3哈希的学号(部分显示)",
      "credit_score": 100
    }
  }
}
```

#### 3.1.4 获取挑战字符串

**接口**：POST /api/v1/auth/challenge
**功能**：获取证书登录用的挑战字符串
**请求参数**：
```json
{
  "student_id": "学号"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "challenge": "随机挑战字符串",
    "expires_in": 300
  }
}
```

### 3.2 自习室管理接口

#### 3.2.1 获取自习室列表

**接口**：GET /api/v1/rooms
**功能**：获取所有自习室信息列表
**查询参数**：
- building: 建筑名称
- floor: 楼层
- status: 状态过滤
- page: 页码
- limit: 每页数量

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 25,
    "items": [
      {
        "id": 1,
        "name": "一号自习室",
        "building": "图书馆主楼",
        "floor": 3,
        "capacity": 100,
        "available_seats": 45,
        "status": "open",
        "open_time": "08:00:00",
        "close_time": "22:00:00"
      },
      // ...其他自习室
    ]
  }
}
```

#### 3.2.2 获取特定自习室信息

**接口**：GET /api/v1/rooms/{id}
**功能**：获取指定自习室的详细信息
**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "一号自习室",
    "building": "图书馆主楼",
    "floor": 3,
    "capacity": 100,
    "available_seats": 45,
    "status": "open",
    "open_time": "08:00:00",
    "close_time": "22:00:00",
    "description": "安静舒适的自习环境，配备空调和充电插座",
    "created_at": "2023-09-01T10:00:00Z",
    "updated_at": "2023-11-25T08:30:00Z"
  }
}
```

### 3.3 座位管理接口

#### 3.3.1 获取自习室座位列表

**接口**：GET /api/v1/rooms/{room_id}/seats
**功能**：获取指定自习室的所有座位信息
**查询参数**：
- status: 座位状态过滤
- is_power_outlet: 是否带电源过滤
- is_window_seat: 是否靠窗过滤
- date: 特定日期的座位可用状态

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "items": [
      {
        "id": 101,
        "room_id": 1,
        "seat_number": "A-01",
        "status": "空闲",
        "position_x": 10,
        "position_y": 20,
        "is_power_outlet": true,
        "is_window_seat": false,
        "time_slots": [
          {
            "start_time": "08:00:00",
            "end_time": "12:00:00",
            "available": true
          },
          {
            "start_time": "13:00:00",
            "end_time": "17:00:00",
            "available": false
          }
        ]
      },
      // ...其他座位
    ]
  }
}
```

#### 3.3.2 获取特定座位信息

**接口**：GET /api/v1/seats/{id}
**功能**：获取指定座位的详细信息
**查询参数**：
- date: 特定日期的座位可用状态

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 101,
    "room_id": 1,
    "room_name": "一号自习室",
    "seat_number": "A-01",
    "status": "空闲",
    "position_x": 10,
    "position_y": 20,
    "is_power_outlet": true,
    "is_window_seat": false,
    "amenities": "USB充电口",
    "time_slots": [
      {
        "start_time": "08:00:00",
        "end_time": "12:00:00",
        "available": true
      },
      {
        "start_time": "13:00:00",
        "end_time": "17:00:00",
        "available": false
      }
    ],
    "last_updated": "2023-11-25T08:30:00Z"
  }
}
```

### 3.4 预约管理接口

#### 3.4.1 创建预约

**接口**：POST /api/v1/reservations
**功能**：用户预约座位
**请求参数**：
```json
{
  "seat_id": 101,
  "start_time": "2023-12-01T08:00:00Z",
  "end_time": "2023-12-01T12:00:00Z",
  "remarks": "自习准备期末考试"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "预约成功",
  "data": {
    "id": 456,
    "seat_id": 101,
    "seat_number": "A-01",
    "room_id": 1,
    "room_name": "一号自习室",
    "start_time": "2023-12-01T08:00:00Z",
    "end_time": "2023-12-01T12:00:00Z",
    "status": "预约中",
    "created_at": "2023-11-25T10:30:00Z"
  }
}
```

#### 3.4.2 获取用户预约列表

**接口**：GET /api/v1/users/me/reservations
**功能**：获取当前用户的预约记录
**查询参数**：
- status: 预约状态过滤
- start_date: 开始日期
- end_date: 结束日期
- page: 页码
- limit: 每页数量

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 12,
    "items": [
      {
        "id": 456,
        "seat_id": 101,
        "seat_number": "A-01",
        "room_id": 1,
        "room_name": "一号自习室",
        "start_time": "2023-12-01T08:00:00Z",
        "end_time": "2023-12-01T12:00:00Z",
        "status": "预约中",
        "created_at": "2023-11-25T10:30:00Z"
      },
      // ...其他预约
    ]
  }
}
```

#### 3.4.3 取消预约

**接口**：POST /api/v1/reservations/{id}/cancel
**功能**：取消指定预约
**请求参数**：
```json
{
  "reason": "有事无法前往"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "预约已取消",
  "data": {
    "id": 456,
    "status": "已取消",
    "cancel_time": "2023-11-26T09:15:00Z"
  }
}
```

### 3.5 签到管理接口

#### 3.5.1 生成签到二维码

**接口**：GET /api/v1/reservations/{id}/qrcode
**功能**：为特定预约生成签到二维码
**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "qrcode_data": {
      "reservation_id": 456,
      "seat_id": 101,
      "user_id": 123,
      "expiry": "2023-12-01T08:15:00Z",
      "nonce": "随机字符串",
      "signature": "SM3签名"
    },
    "expires_in": 900
  }
}
```

#### 3.5.2 验证签到二维码

**接口**：POST /api/v1/check-in/verify
**功能**：验证签到二维码并完成签到
**请求参数**：
```json
{
  "qrcode_data": {
    "reservation_id": 456,
    "seat_id": 101,
    "user_id": 123,
    "expiry": "2023-12-01T08:15:00Z",
    "nonce": "随机字符串",
    "signature": "SM3签名"
  }
}
```
**响应**：
```json
{
  "code": 0,
  "message": "签到成功",
  "data": {
    "reservation_id": 456,
    "seat_id": 101,
    "seat_number": "A-01",
    "check_in_time": "2023-12-01T07:55:00Z",
    "status": "已签到"
  }
}
```

#### 3.5.3 手动签到

**接口**：POST /api/v1/reservations/{id}/check-in
**功能**：通过预约ID直接签到（管理员功能）
**请求参数**：
```json
{
  "check_in_location": "位置信息(可选)",
  "admin_remarks": "管理员备注(可选)"
}
```
**响应**：
```json
{
  "code": 0,
  "message": "签到成功",
  "data": {
    "reservation_id": 456,
    "seat_id": 101,
    "seat_number": "A-01",
    "check_in_time": "2023-12-01T07:55:00Z",
    "status": "已签到"
  }
}
```

## 四、安全与加密设计

### 4.1 通信加密流程

本系统采用多层次的通信加密机制，确保数据传输安全：

1. **基础传输加密**：使用HTTPS协议，提供传输层安全
2. **请求签名**：敏感请求采用SM3算法签名，防止篡改
3. **数据加密**：敏感数据采用SM2/SM4混合加密，确保机密性

#### 4.1.1 请求签名生成流程

1. 客户端按照字母顺序对所有参数进行排序
2. 将参数名和参数值拼接成字符串，形如`key1=value1&key2=value2`
3. 添加时间戳和nonce防止重放攻击
4. 使用SM3算法计算签名
5. 在请求头中添加签名信息`X-Signature`

```javascript
// 生成请求签名
function generateSignature(params, secretKey) {
  // 添加时间戳和nonce
  params.timestamp = Date.now();
  params.nonce = generateRandomNonce();
  
  // 按字母排序
  const sortedKeys = Object.keys(params).sort();
  
  // 拼接参数
  let signStr = '';
  sortedKeys.forEach(key => {
    signStr += `${key}=${params[key]}&`;
  });
  
  // 添加密钥
  signStr += secretKey;
  
  // 计算SM3哈希
  return sm3.hash(signStr);
}
```

#### 4.1.2 SM2/SM4混合加密流程

敏感API调用采用以下加密流程：

1. 客户端生成随机SM4对称密钥
2. 使用SM4密钥加密实际数据
3. 使用服务端SM2公钥加密SM4密钥
4. 将加密的数据和密钥一起发送到服务端
5. 服务端使用SM2私钥解密SM4密钥
6. 使用解密出的SM4密钥解密数据

```javascript
// 前端加密数据
function encryptRequestData(data, serverPublicKey) {
  // 生成随机SM4密钥
  const sm4Key = generateRandomKey(16);
  
  // 使用SM4加密数据
  const encryptedData = sm4.encrypt(JSON.stringify(data), sm4Key);
  
  // 使用服务端公钥加密SM4密钥
  const encryptedKey = sm2.encrypt(sm4Key, serverPublicKey);
  
  return {
    encryptedData: encryptedData,
    encryptedKey: encryptedKey,
    timestamp: Date.now(),
    nonce: generateRandomNonce()
  };
}
```

### 4.2 认证与授权设计

#### 4.2.1 JWT令牌结构

```json
{
  "header": {
    "alg": "SM3withSM2",
    "typ": "JWT"
  },
  "payload": {
    "sub": "123",  // 用户ID
    "iat": 1638320000,  // 签发时间
    "exp": 1638323600,  // 过期时间
    "role": "user",  // 用户角色
    "jti": "随机UUID"  // JWT ID，用于令牌撤销
  },
  "signature": "使用SM2私钥对header和payload的签名"
}
```

#### 4.2.2 权限控制设计

系统采用基于角色(RBAC)的权限控制机制，主要角色包括：

1. **匿名用户**：未登录用户，只能访问公开接口
2. **普通用户**：已登录的学生，可以预约座位、查看个人信息等
3. **管理员**：可以管理座位、审核违规记录等
4. **超级管理员**：拥有全部权限，包括系统配置等

权限检查流程：

1. 解析请求头中的JWT令牌
2. 验证令牌签名
3. 检查令牌是否过期
4. 提取用户角色和权限信息
5. 根据接口所需权限进行校验

```python
# 权限校验装饰器示例
def permission_required(permission):
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(request, *args, **kwargs):
            # 获取用户权限
            user_permissions = get_user_permissions(request.user)
            
            # 检查权限
            if permission not in user_permissions:
                return JsonResponse({
                    'code': 403,
                    'message': '权限不足'
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapped_view
    return decorator
```

## 五、WebSocket实时通信

### 5.1 WebSocket连接设计

系统使用WebSocket提供实时通信功能，主要用于座位状态更新、预约通知等场景。

#### 5.1.1 连接建立

**连接URL**：`wss://api.example.com/ws/`
**认证方式**：通过URL参数或连接后的认证消息传递令牌：
```
wss://api.example.com/ws/?token=JWT令牌
```

或者连接后发送认证消息：
```json
{
  "type": "authenticate",
  "token": "JWT令牌"
}
```

#### 5.1.2 消息格式

**客户端消息**：
```json
{
  "type": "subscribe",
  "channel": "seat_status",
  "room_id": 1
}
```

**服务端消息**：
```json
{
  "type": "seat_status_update",
  "data": {
    "seat_id": 101,
    "status": "已预约",
    "last_updated": "2023-12-01T08:05:00Z"
  }
}
```

### 5.2 主要WebSocket事件

| 事件类型 | 方向 | 说明 | 示例数据 |
|---------|------|------|---------|
| authenticate | 客户端→服务端 | 认证 | `{"type":"authenticate","token":"JWT令牌"}` |
| subscribe | 客户端→服务端 | 订阅频道 | `{"type":"subscribe","channel":"seat_status","room_id":1}` |
| unsubscribe | 客户端→服务端 | 取消订阅 | `{"type":"unsubscribe","channel":"seat_status","room_id":1}` |
| seat_status_update | 服务端→客户端 | 座位状态更新 | `{"type":"seat_status_update","data":{"seat_id":101,"status":"已预约"}}` |
| reservation_reminder | 服务端→客户端 | 预约提醒 | `{"type":"reservation_reminder","data":{"reservation_id":456,"start_time":"2023-12-01T08:00:00Z"}}` |

## 六、API文档与测试

### 6.1 API文档部署

系统将提供自动生成的API文档，便于开发人员和前端团队参考：

1. **Swagger UI**：访问路径`/api/docs/`，提供交互式API文档
2. **ReDoc**：访问路径`/api/redoc/`，提供美观、易于阅读的API文档
3. **OpenAPI规范文件**：提供OpenAPI 3.0格式的API定义，位于`/api/schema/`

### 6.2 API测试工具

为确保API的正确性和性能，建议使用以下测试工具：

1. **Postman**：手动测试API接口
2. **JMeter**：性能测试，模拟多用户并发
3. **pytest**：自动化API测试用例

### 6.3 测试用例示例

```python
# 测试预约创建API的示例测试用例
def test_create_reservation():
    # 准备测试数据
    test_data = {
        "seat_id": 101,
        "start_time": "2023-12-01T08:00:00Z",
        "end_time": "2023-12-01T12:00:00Z"
    }
    
    # 获取认证令牌
    auth_token = get_test_user_token()
    
    # 发送请求
    response = client.post(
        "/api/v1/reservations",
        json=test_data,
        headers={"Authorization": f"Bearer {auth_token}"}
    )
    
    # 验证响应
    assert response.status_code == 201
    assert response.json()["code"] == 0
    assert "id" in response.json()["data"]
    assert response.json()["data"]["seat_id"] == 101
    assert response.json()["data"]["status"] == "预约中"
```

## 七、接口安全检查清单

为确保API接口的安全性，系统将实施以下安全措施：

- [x] 所有接口使用HTTPS传输
- [x] 敏感数据使用SM2/SM4加密
- [x] 使用JWT进行身份认证
- [x] 实施适当的请求频率限制
- [x] 实施防重放攻击机制
- [x] 输入验证与参数过滤
- [x] 错误信息不泄露敏感信息
- [x] 定期安全审计和渗透测试
- [x] 记录所有API访问日志