"""
Celery配置文件
使用内存作为消息代理，使用Django缓存作为结果后端
"""
import os
from celery import Celery
import logging

# 设置默认Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# 创建Celery应用
app = Celery('backend')

# 使用字符串表示，这样worker不用序列化配置对象
app.config_from_object('django.conf:settings', namespace='CELERY')

# 从所有已注册的Django应用中加载任务模块
app.autodiscover_tasks()

# 配置日志
logger = logging.getLogger(__name__)

@app.task(bind=True)
def debug_task(self):
    """测试任务"""
    logger.info(f"执行测试任务: {self.request!r}")
    return {'status': 'success', 'message': '测试任务执行成功'}
