from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.authentication.models import User
from utils.crypto import SM2Crypto
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '为现有用户添加SM2公钥和私钥'

    def add_arguments(self, parser):
        parser.add_argument('--all', action='store_true', help='为所有没有公钥的用户添加')
        parser.add_argument('--student-ids', nargs='+', type=str, help='指定要添加公钥的学号列表')
        parser.add_argument('--output-file', type=str, help='将生成的密钥对保存到文件')

    def handle(self, *args, **options):
        all_users = options['all']
        student_ids = options['student_ids']
        output_file = options['output_file']
        
        if not all_users and not student_ids:
            self.stdout.write(self.style.ERROR('请指定 --all 或 --student-ids 参数'))
            return
        
        # 获取用户列表
        if all_users:
            users = User.objects.filter(public_key__isnull=True)
            self.stdout.write(f'找到 {users.count()} 个没有公钥的用户')
        else:
            # 将学号转换为哈希值进行查询
            from utils.crypto import SM3Hasher
            student_id_hashes = [SM3Hasher.hash(sid) for sid in student_ids]
            users = User.objects.filter(student_id_hash__in=student_id_hashes)
            self.stdout.write(f'找到 {users.count()} 个指定学号的用户')
        
        if not users.exists():
            self.stdout.write(self.style.WARNING('没有找到符合条件的用户'))
            return
        
        # 保存密钥对信息到文件
        if output_file:
            with open(output_file, 'w') as f:
                f.write("学号哈希,公钥,私钥\n")
        
        # 为每个用户生成密钥对
        updated_count = 0
        for user in users:
            try:
                # 生成SM2密钥对
                key_pair = SM2Crypto.generate_key_pair()
                private_key = key_pair['private_key']
                public_key = key_pair['public_key']
                
                # 更新用户公钥
                user.public_key = public_key
                # 设置公钥过期时间（1年后）
                user.public_key_expires = timezone.now() + timezone.timedelta(days=365)
                user.save()
                
                updated_count += 1
                self.stdout.write(f'为用户 {user.student_id_hash} 添加了SM2公钥')
                
                # 保存密钥对信息到文件
                if output_file:
                    with open(output_file, 'a') as f:
                        f.write(f"{user.student_id_hash},{public_key},{private_key}\n")
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'为用户 {user.student_id_hash} 添加SM2公钥时出错: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'成功为 {updated_count} 个用户添加了SM2公钥'))
