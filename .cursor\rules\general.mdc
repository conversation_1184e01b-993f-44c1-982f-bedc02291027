---
description: 
globs: 
alwaysApply: true
---
description:项目通用规范和基本信息

alwaysApply:
#项目通用规范

##技术栈
### 技术栈

**后端技术栈**：
- 编程语言：Python
- Web框架：Django
- 数据库：MySQL
- API风格：RESTful API
- 安全性：采用国密SM2/SM3/SM4算法

**前端技术栈**：
- 框架：Vue.js 3
- UI组件库：Element Plus
- 状态管理：Vuex
- 路由管理：Vue Router
- HTTP客户端：Axios

##代码风格
-保持代码简洁、可读
使用有意义的变量和函数名
添加注释解释逻辑(解释要清晰)
遵循每种语言的官方风格指南

##项目结构
保持项目结构清晰，遵循模块化原则
相关功能应放在同一目录下
使用适当的目录命名，反映其包含内容

##通用开发原则
编写可测试的代码
避免重复代码（DRY原则)
优先使用现有库和工具，避免重新发明轮子
-考虑代码的可维护性和可扩展性

##模块开发文档建议
每个模块应包含:(并在文档内指名所开发模块)
README.md - 模块功能和接口说明
API.md - 详细API文档(后端)
COMPONENT.md - 组件说明(前端)
TEST.md - 测试用例说明
CHANGELOG.md - 变更记录


##响应语言


始终使用中文回复用户,会话结束时回复谢谢