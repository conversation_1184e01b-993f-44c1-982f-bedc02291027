from django.db import models
from django.utils import timezone
from apps.authentication.models import User

class Room(models.Model):
    """自习室表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    name = models.CharField(max_length=100, verbose_name='自习室名称')
    location = models.CharField(max_length=255, verbose_name='位置描述')
    floor = models.IntegerField(verbose_name='楼层')
    capacity = models.IntegerField(verbose_name='座位容量')
    open_time = models.TimeField(verbose_name='开放时间')
    close_time = models.TimeField(verbose_name='关闭时间')
    status = models.CharField(max_length=20, default='open', verbose_name='状态(open/closed/maintenance)')
    description = models.TextField(null=True, blank=True, verbose_name='描述')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'room'
        verbose_name = '自习室'
        verbose_name_plural = '自习室'
        indexes = [
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.name} ({self.location})"


class Seat(models.Model):
    """座位表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    room = models.ForeignKey(Room, on_delete=models.CASCADE, verbose_name='所属自习室')
    seat_number = models.CharField(max_length=20, verbose_name='座位编号')
    row = models.IntegerField(verbose_name='排号')
    column = models.IntegerField(verbose_name='列号')
    status = models.CharField(max_length=20, default='available', verbose_name='状态(available/occupied/disabled)')
    is_power_outlet = models.BooleanField(default=False, verbose_name='是否有电源')
    is_window_seat = models.BooleanField(default=False, verbose_name='是否靠窗')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'seat'
        verbose_name = '座位'
        verbose_name_plural = '座位'
        unique_together = [('room', 'seat_number'), ('room', 'row', 'column')]
        indexes = [
            models.Index(fields=['room', 'status']),
            models.Index(fields=['is_power_outlet']),
            models.Index(fields=['is_window_seat']),
        ]

    def __str__(self):
        return f"{self.room.name} - {self.seat_number}"


class Reservation(models.Model):
    """预约表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    seat = models.ForeignKey(Seat, on_delete=models.CASCADE, verbose_name='座位')
    reservation_code = models.CharField(max_length=64, unique=True, verbose_name='预约码(SM3哈希)')
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    status = models.CharField(max_length=20, default='pending', verbose_name='状态(pending/checked_in/completed/cancelled/timeout)')
    check_in_time = models.DateTimeField(null=True, blank=True, verbose_name='签到时间')
    check_out_time = models.DateTimeField(null=True, blank=True, verbose_name='签退时间')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    signature = models.CharField(max_length=128, null=True, blank=True, verbose_name='SM2签名')

    class Meta:
        db_table = 'reservation'
        verbose_name = '预约'
        verbose_name_plural = '预约'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['seat']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
            models.Index(fields=['end_time']),
        ]

    def __str__(self):
        return f"Reservation {self.id} - {self.user.student_id_hash[:10]}... - {self.seat.seat_number}"


class SeatOperation(models.Model):
    """座位操作记录表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    seat = models.ForeignKey(Seat, on_delete=models.CASCADE, verbose_name='座位')
    operation_type = models.CharField(max_length=20, verbose_name='操作类型(reserve/check_in/check_out/cancel/timeout)')
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='用户')
    reservation = models.ForeignKey(Reservation, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联预约')
    status_before = models.CharField(max_length=20, verbose_name='操作前状态')
    status_after = models.CharField(max_length=20, verbose_name='操作后状态')
    ip_address = models.CharField(max_length=45, null=True, blank=True, verbose_name='IP地址')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    hash_value = models.CharField(max_length=64, verbose_name='SM3哈希值')
    prev_hash = models.CharField(max_length=64, null=True, blank=True, verbose_name='前一条记录的哈希值')

    class Meta:
        db_table = 'seat_operation'
        verbose_name = '座位操作记录'
        verbose_name_plural = '座位操作记录'
        indexes = [
            models.Index(fields=['seat']),
            models.Index(fields=['operation_type']),
            models.Index(fields=['user']),
            models.Index(fields=['reservation']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Seat Operation {self.id} - {self.operation_type}"


class BlacklistRecord(models.Model):
    """黑名单记录表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    reason = models.CharField(max_length=255, verbose_name='加入黑名单原因')
    start_time = models.DateTimeField(default=timezone.now, verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    status = models.CharField(max_length=20, default='active', verbose_name='状态(active/expired/cancelled)')
    operator_id = models.BigIntegerField(null=True, blank=True, verbose_name='操作员ID')
    operator_type = models.CharField(max_length=10, default='system', verbose_name='操作员类型(system/admin)')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'blacklist_record'
        verbose_name = '黑名单记录'
        verbose_name_plural = '黑名单记录'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
            models.Index(fields=['end_time']),
        ]

    def __str__(self):
        return f"Blacklist {self.id} - User {self.user_id}"
