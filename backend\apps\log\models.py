from django.db import models
from django.utils import timezone
from apps.authentication.models import User

class SystemLog(models.Model):
    """系统日志表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    log_type = models.CharField(max_length=20, verbose_name='日志类型(info/warning/error/security)')
    module = models.CharField(max_length=50, verbose_name='模块名称')
    action = models.CharField(max_length=50, verbose_name='操作名称')
    description = models.TextField(verbose_name='日志描述')
    ip_address = models.CharField(max_length=45, null=True, blank=True, verbose_name='IP地址')
    user_id = models.BigIntegerField(null=True, blank=True, verbose_name='用户ID')
    user_type = models.CharField(max_length=10, null=True, blank=True, verbose_name='用户类型(user/admin)')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    hash_value = models.Char<PERSON>ield(max_length=64, verbose_name='SM3哈希值')
    prev_hash = models.CharField(max_length=64, null=True, blank=True, verbose_name='前一条记录的哈希值')

    class Meta:
        db_table = 'system_log'
        verbose_name = '系统日志'
        verbose_name_plural = '系统日志'
        indexes = [
            models.Index(fields=['log_type']),
            models.Index(fields=['module']),
            models.Index(fields=['action']),
            models.Index(fields=['user_id']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"System Log {self.id} - {self.log_type} - {self.module}.{self.action}"


class UserActionLog(models.Model):
    """用户操作日志表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    action = models.CharField(max_length=50, verbose_name='操作名称')
    description = models.TextField(verbose_name='操作描述')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    target_type = models.CharField(max_length=20, null=True, blank=True, verbose_name='目标类型')
    target_id = models.BigIntegerField(null=True, blank=True, verbose_name='目标ID')
    status = models.CharField(max_length=20, default='success', verbose_name='状态(success/failed)')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    hash_value = models.CharField(max_length=64, verbose_name='SM3哈希值')
    prev_hash = models.CharField(max_length=64, null=True, blank=True, verbose_name='前一条记录的哈希值')

    class Meta:
        db_table = 'user_action_log'
        verbose_name = '用户操作日志'
        verbose_name_plural = '用户操作日志'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['action']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"User Action Log {self.id} - User {self.user_id} - {self.action}"


class SecurityLog(models.Model):
    """安全日志表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    log_type = models.CharField(max_length=20, verbose_name='日志类型(login_attempt/auth_failure/token_issue/token_revoke/password_change)')
    user_id = models.BigIntegerField(null=True, blank=True, verbose_name='用户ID')
    user_type = models.CharField(max_length=10, null=True, blank=True, verbose_name='用户类型(user/admin)')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    description = models.TextField(verbose_name='日志描述')
    status = models.CharField(max_length=20, default='success', verbose_name='状态(success/failed)')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    hash_value = models.CharField(max_length=64, verbose_name='SM3哈希值')
    prev_hash = models.CharField(max_length=64, null=True, blank=True, verbose_name='前一条记录的哈希值')

    class Meta:
        db_table = 'security_log'
        verbose_name = '安全日志'
        verbose_name_plural = '安全日志'
        indexes = [
            models.Index(fields=['log_type']),
            models.Index(fields=['user_id']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Security Log {self.id} - {self.log_type} - {self.status}"


class ApiRequestLog(models.Model):
    """API请求日志表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    request_id = models.CharField(max_length=64, unique=True, verbose_name='请求ID')
    user_id = models.BigIntegerField(null=True, blank=True, verbose_name='用户ID')
    user_type = models.CharField(max_length=10, null=True, blank=True, verbose_name='用户类型(user/admin)')
    method = models.CharField(max_length=10, verbose_name='HTTP方法')
    path = models.CharField(max_length=255, verbose_name='请求路径')
    query_params = models.TextField(null=True, blank=True, verbose_name='查询参数')
    request_body = models.TextField(null=True, blank=True, verbose_name='请求体')
    response_status = models.IntegerField(verbose_name='响应状态码')
    response_time = models.IntegerField(verbose_name='响应时间(毫秒)')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    hash_value = models.CharField(max_length=64, verbose_name='SM3哈希值')
    prev_hash = models.CharField(max_length=64, null=True, blank=True, verbose_name='前一条记录的哈希值')

    class Meta:
        db_table = 'api_request_log'
        verbose_name = 'API请求日志'
        verbose_name_plural = 'API请求日志'
        indexes = [
            models.Index(fields=['request_id']),
            models.Index(fields=['user_id']),
            models.Index(fields=['method']),
            models.Index(fields=['path']),
            models.Index(fields=['response_status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"API Request Log {self.id} - {self.method} {self.path} - {self.response_status}"


class CeleryTask(models.Model):
    """Celery任务模型"""
    task_id = models.CharField(max_length=255, unique=True, verbose_name='任务ID')
    task_name = models.CharField(max_length=255, verbose_name='任务名称')
    task_args = models.TextField(null=True, blank=True, verbose_name='任务参数')
    task_kwargs = models.TextField(null=True, blank=True, verbose_name='任务关键字参数')
    status = models.CharField(max_length=50, verbose_name='任务状态')
    result = models.TextField(null=True, blank=True, verbose_name='任务结果')
    traceback = models.TextField(null=True, blank=True, verbose_name='错误追踪')
    date_created = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    date_done = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')

    class Meta:
        db_table = 'celery_task'
        verbose_name = 'Celery任务'
        verbose_name_plural = 'Celery任务'
        indexes = [
            models.Index(fields=['task_id']),
            models.Index(fields=['status']),
            models.Index(fields=['date_created']),
        ]

    def __str__(self):
        return f"{self.task_name} ({self.task_id})"
