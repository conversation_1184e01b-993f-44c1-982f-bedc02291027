<template>
  <header class="app-header">
    <div class="logo-container">
      <router-link to="/">
        <h1 class="site-title">基于国密算法的图书馆自习室座位管理系统</h1>
      </router-link>
    </div>
    <div class="nav-container">
      <!-- 开发模式：始终显示用户信息 -->
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="user-info">
          测试用户 (202400001)
          <el-icon><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人中心</el-dropdown-item>
            <el-dropdown-item command="reservations">我的预约</el-dropdown-item>
            <el-dropdown-item command="records">操作记录</el-dropdown-item>
            <el-dropdown-item divided command="logout"
              >退出登录</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="credit-score">
        信誉分:
        <span class="score-excellent">95</span>
      </div>

      <!-- 登录/注册按钮（开发模式下隐藏） -->
      <!--
      <template v-if="!isLoggedIn">
        <el-button type="primary" @click="$router.push('/login')">登录</el-button>
        <el-button @click="$router.push('/register')">注册</el-button>
      </template>
      -->
    </div>
  </header>
</template>

<script>
import { computed } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";

export default {
  name: "AppHeader",
  components: {
    ArrowDown,
  },
  setup() {
    const store = useStore();
    const router = useRouter();

    const isLoggedIn = computed(() => store.getters["user/isLoggedIn"]);
    const userInfo = computed(() => store.getters["user/userInfo"]);

    const creditScoreClass = computed(() => {
      const score = userInfo.value.creditScore || 100;
      if (score >= 90) return "score-excellent";
      if (score >= 70) return "score-good";
      if (score >= 50) return "score-warning";
      return "score-danger";
    });

    const handleCommand = async (command) => {
      switch (command) {
        case "profile":
          router.push("/user/profile");
          break;
        case "reservations":
          router.push("/user/reservations");
          break;
        case "records":
          router.push("/user/records");
          break;
        case "logout":
          try {
            await ElMessageBox.confirm("确定要退出登录吗？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            });
            await store.dispatch("user/logout");
            ElMessage.success("退出登录成功");
            router.push("/login");
          } catch (error) {
            // 用户取消操作
          }
          break;
      }
    };

    return {
      isLoggedIn,
      userInfo,
      creditScoreClass,
      handleCommand,
    };
  },
};
</script>

<style lang="scss" scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;

  a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
  }

  /* 删除logo相关样式 */

  .site-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
  }
}

.nav-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;

  .el-icon {
    margin-left: 5px;
  }
}

.credit-score {
  margin-left: 15px;
  font-size: 0.9rem;

  .score-excellent {
    color: #67c23a;
    font-weight: bold;
  }

  .score-good {
    color: #409eff;
    font-weight: bold;
  }

  .score-warning {
    color: #e6a23c;
    font-weight: bold;
  }

  .score-danger {
    color: #f56c6c;
    font-weight: bold;
  }
}
</style>
