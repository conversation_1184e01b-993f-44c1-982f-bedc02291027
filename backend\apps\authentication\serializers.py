"""
用户认证序列化器
"""
import binascii
import secrets
import base64
import logging
from django.utils import timezone
from rest_framework import serializers
from .models import User, UserSession, CreditRecord
from utils.crypto import SM3Hasher, SM4Crypto, SM2Crypto

logger = logging.getLogger(__name__)

class UserRegisterSerializer(serializers.Serializer):
    """用户注册序列化器"""
    student_id = serializers.CharField(max_length=20, required=True, help_text='学号')
    password = serializers.CharField(max_length=64, required=True, write_only=True, help_text='密码')
    email = serializers.EmailField(required=False, help_text='邮箱')
    phone = serializers.CharField(max_length=20, required=False, help_text='手机号')

    def validate(self, attrs):
        student_id = attrs.get('student_id')

        # 计算学号的SM3哈希值用于检查是否已存在
        student_id_hash = SM3Hasher.hash(student_id)

        # 检查学号是否已被注册
        if User.objects.filter(student_id_hash=student_id_hash).exists():
            raise serializers.ValidationError({'student_id': '该学号已被注册'})

        return attrs

    def create(self, validated_data):
        student_id = validated_data.get('student_id')
        password = validated_data.get('password')
        email = validated_data.get('email')
        phone = validated_data.get('phone')

        # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
        sm4_key = SM4Crypto.generate_key()

        # 加密学号
        encrypted_student_id = SM4Crypto.encrypt(sm4_key, student_id)
        student_id_binary = binascii.unhexlify(encrypted_student_id)

        # 计算学号的SM3哈希值用于索引
        student_id_hash = SM3Hasher.hash(student_id)

        # 使用SM3哈希密码
        password_hash_result = SM3Hasher.hash_with_salt(password)

        # 创建用户对象
        user = User(
            student_id=student_id_binary,
            student_id_hash=student_id_hash,
            password=password_hash_result['hash'],
            salt=password_hash_result['salt'],
            iterations=password_hash_result['iterations']
        )

        # 如果提供了邮箱，加密存储
        if email:
            encrypted_email = SM4Crypto.encrypt(sm4_key, email)
            user.email = binascii.unhexlify(encrypted_email)

        # 如果提供了手机号，加密存储
        if phone:
            encrypted_phone = SM4Crypto.encrypt(sm4_key, phone)
            user.phone = binascii.unhexlify(encrypted_phone)

        user.save()
        return user


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    student_id = serializers.CharField(max_length=20, required=True, help_text='学号')
    password = serializers.CharField(max_length=64, required=True, write_only=True, help_text='密码')

    def validate(self, attrs):
        student_id = attrs.get('student_id')
        password = attrs.get('password')

        # 计算学号的SM3哈希值用于查询
        student_id_hash = SM3Hasher.hash(student_id)

        try:
            user = User.objects.get(student_id_hash=student_id_hash)
        except User.DoesNotExist:
            raise serializers.ValidationError({'student_id': '学号或密码错误'})

        # 检查用户状态
        if user.status != 'active':
            raise serializers.ValidationError({'student_id': f'账号状态异常: {user.status}'})

        # 验证密码
        if not SM3Hasher.verify(password, user.password, user.salt, user.iterations):
            # 增加登录失败次数
            user.login_attempts += 1
            if user.login_attempts >= 5:
                user.status = 'locked'
            user.save()
            raise serializers.ValidationError({'password': '学号或密码错误'})

        # 重置登录失败次数
        user.login_attempts = 0
        user.save()

        # 将用户对象添加到验证后的数据中
        attrs['user'] = user
        return attrs


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    class Meta:
        model = User
        fields = ['id', 'student_id_hash', 'status', 'credit_score', 'last_login', 'created_at']
        read_only_fields = ['id', 'student_id_hash', 'credit_score', 'last_login', 'created_at']


class UserDetailSerializer(serializers.ModelSerializer):
    """用户详情序列化器"""
    student_id = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'student_id', 'student_id_hash', 'email', 'phone', 'status',
                  'credit_score', 'last_login', 'created_at', 'updated_at']
        read_only_fields = ['id', 'student_id_hash', 'credit_score', 'last_login',
                           'created_at', 'updated_at']

    def get_student_id(self, obj):
        # 实际应用中应该使用系统配置的密钥
        sm4_key = SM4Crypto.generate_key()
        try:
            encrypted_student_id = binascii.hexlify(obj.student_id).decode()
            return SM4Crypto.decrypt(sm4_key, encrypted_student_id)
        except Exception:
            return None

    def get_email(self, obj):
        if not obj.email:
            return None

        # 实际应用中应该使用系统配置的密钥
        sm4_key = SM4Crypto.generate_key()
        try:
            encrypted_email = binascii.hexlify(obj.email).decode()
            return SM4Crypto.decrypt(sm4_key, encrypted_email)
        except Exception:
            return None

    def get_phone(self, obj):
        if not obj.phone:
            return None

        # 实际应用中应该使用系统配置的密钥
        sm4_key = SM4Crypto.generate_key()
        try:
            encrypted_phone = binascii.hexlify(obj.phone).decode()
            return SM4Crypto.decrypt(sm4_key, encrypted_phone)
        except Exception:
            return None


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器"""
    password = serializers.CharField(max_length=64, required=False, write_only=True)
    old_password = serializers.CharField(max_length=64, required=False, write_only=True)
    email = serializers.EmailField(required=False)
    phone = serializers.CharField(max_length=20, required=False)
    public_key = serializers.CharField(required=False)

    class Meta:
        model = User
        fields = ['password', 'old_password', 'email', 'phone', 'public_key']

    def validate(self, attrs):
        # 如果要更新密码，需要验证旧密码
        if 'password' in attrs and 'old_password' not in attrs:
            raise serializers.ValidationError({'old_password': '更新密码需要提供旧密码'})

        if 'password' in attrs and 'old_password' in attrs:
            user = self.instance
            old_password = attrs.pop('old_password')

            # 验证旧密码
            if not SM3Hasher.verify(old_password, user.password, user.salt, user.iterations):
                raise serializers.ValidationError({'old_password': '旧密码错误'})

        # 验证公钥格式
        if 'public_key' in attrs:
            public_key = attrs['public_key']
            try:
                # 尝试使用公钥进行一次加密操作来验证其有效性
                SM2Crypto.encrypt(public_key, "test")
            except Exception as e:
                logger.error(f"无效的SM2公钥: {str(e)}")
                raise serializers.ValidationError({'public_key': '无效的SM2公钥格式'})

        return attrs

    def update(self, instance, validated_data):
        # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
        sm4_key = SM4Crypto.generate_key()

        # 更新密码
        if 'password' in validated_data:
            password = validated_data.pop('password')
            password_hash_result = SM3Hasher.hash_with_salt(password)
            instance.password = password_hash_result['hash']
            instance.salt = password_hash_result['salt']
            instance.iterations = password_hash_result['iterations']

            # 记录密码修改
            logger.info(f"用户 {instance.student_id_hash} 修改了密码")

        # 更新邮箱
        if 'email' in validated_data:
            email = validated_data.pop('email')
            encrypted_email = SM4Crypto.encrypt(sm4_key, email)
            instance.email = binascii.unhexlify(encrypted_email)

            # 记录邮箱修改
            logger.info(f"用户 {instance.student_id_hash} 修改了邮箱")

        # 更新手机号
        if 'phone' in validated_data:
            phone = validated_data.pop('phone')
            encrypted_phone = SM4Crypto.encrypt(sm4_key, phone)
            instance.phone = binascii.unhexlify(encrypted_phone)

            # 记录手机号修改
            logger.info(f"用户 {instance.student_id_hash} 修改了手机号")

        # 更新公钥
        if 'public_key' in validated_data:
            instance.public_key = validated_data.pop('public_key')
            # 设置公钥过期时间（1年后）
            instance.public_key_expires = timezone.now() + timezone.timedelta(days=365)

            # 记录公钥修改
            logger.info(f"用户 {instance.student_id_hash} 更新了SM2公钥")

        instance.save()
        return instance


class CreditRecordSerializer(serializers.ModelSerializer):
    """信誉分记录序列化器"""
    class Meta:
        model = CreditRecord
        fields = ['id', 'user', 'delta', 'reason', 'score_after', 'operator_type',
                  'operator_id', 'related_entity', 'related_id', 'created_at']
        read_only_fields = ['id', 'user', 'delta', 'score_after', 'operator_type',
                           'operator_id', 'related_entity', 'related_id', 'created_at']


class PasswordResetRequestSerializer(serializers.Serializer):
    """密码重置请求序列化器"""
    student_id = serializers.CharField(max_length=20, required=True)
    email = serializers.EmailField(required=True)

    def validate(self, attrs):
        """验证学号和邮箱是否匹配"""
        student_id = attrs.get('student_id')
        email = attrs.get('email')

        # 计算学号的SM3哈希值用于查询
        student_id_hash = SM3Hasher.hash(student_id)

        try:
            # 查询用户
            user = User.objects.get(student_id_hash=student_id_hash)

            # 检查用户状态
            if user.status != 'active':
                raise serializers.ValidationError(f"账号状态异常: {user.status}")

            # 检查用户是否有邮箱
            if not user.email:
                raise serializers.ValidationError("该账号未绑定邮箱")

            # 解密邮箱
            sm4_key = SM4Crypto.generate_key()
            encrypted_email = binascii.hexlify(user.email).decode()
            user_email = SM4Crypto.decrypt(sm4_key, encrypted_email)

            # 验证邮箱是否匹配
            if user_email != email:
                raise serializers.ValidationError("邮箱与账号不匹配")

            # 返回验证通过的用户
            attrs['user'] = user
            return attrs
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")


class PasswordResetConfirmSerializer(serializers.Serializer):
    """密码重置确认序列化器"""
    token = serializers.CharField(required=True)
    new_password = serializers.CharField(max_length=20, required=True)

    def validate_new_password(self, value):
        """验证新密码强度"""
        if len(value) < 8:
            raise serializers.ValidationError("密码长度不能少于8个字符")
        return value


class SM2ChallengeSerializer(serializers.Serializer):
    """SM2挑战生成序列化器"""
    student_id = serializers.CharField(max_length=20, required=True)


class SM2LoginSerializer(serializers.Serializer):
    """SM2证书登录序列化器"""
    student_id = serializers.CharField(max_length=20, required=True)
    signature = serializers.CharField(required=True)
