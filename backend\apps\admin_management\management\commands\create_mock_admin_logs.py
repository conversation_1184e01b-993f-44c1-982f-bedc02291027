from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.admin_management.models import Admin, AdminOperationLog
from apps.log.models import SystemLog, SecurityLog, ApiRequestLog
from utils.crypto import SM3Hasher
import random
import datetime
import ipaddress
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '创建模拟管理员操作日志和系统日志数据'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=20, help='要创建的日志数量')
        parser.add_argument('--days', type=int, default=7, help='日志分布的天数')
        parser.add_argument('--include-errors', action='store_true', help='是否包含错误日志')

    def handle(self, *args, **options):
        count = options['count']
        days = options['days']
        include_errors = options['include_errors']
        
        # 获取所有管理员
        admins = Admin.objects.all()
        if not admins.exists():
            self.stdout.write(self.style.ERROR('没有找到管理员，请先创建管理员'))
            return
        
        self.stdout.write(f'开始创建 {count} 条模拟管理员操作日志...')
        
        # 操作类型列表
        operations = [
            'login', 'logout', 'create_config', 'update_config', 'delete_config',
            'update_admin', 'change_password', 'view_logs', 'export_logs',
            'create_room', 'update_room', 'delete_room', 'create_seat', 'update_seat',
            'blacklist_user', 'unblacklist_user', 'reset_user_password'
        ]
        
        # 目标类型列表
        target_types = [
            'admin', 'system_config', 'room', 'seat', 'user', 'blacklist', None
        ]
        
        # 状态列表
        statuses = ['success', 'failed']
        
        # 生成随机IP地址
        def random_ip():
            return str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))
        
        # 创建管理员操作日志
        created_count = 0
        error_count = 0
        
        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)
        
        for i in range(count):
            try:
                # 随机选择管理员
                admin = random.choice(admins)
                
                # 随机选择操作类型
                operation = random.choice(operations)
                
                # 随机选择目标类型
                target_type = random.choice(target_types)
                
                # 随机生成目标ID
                target_id = random.randint(1, 100) if target_type else None
                
                # 随机选择状态（大部分成功，少部分失败）
                status = 'failed' if (include_errors and random.random() < 0.2) else 'success'
                
                # 生成描述
                description = f'管理员{admin.username}执行了{operation}操作'
                if target_type:
                    description += f'，目标类型：{target_type}，目标ID：{target_id}'
                if status == 'failed':
                    description += '，操作失败'
                    
                    # 添加错误原因
                    error_reasons = [
                        '权限不足',
                        '参数无效',
                        '目标不存在',
                        '数据库错误',
                        '网络超时',
                        '系统内部错误'
                    ]
                    description += f'，原因：{random.choice(error_reasons)}'
                
                # 随机生成创建时间
                random_seconds = random.randint(0, int((end_date - start_date).total_seconds()))
                created_at = start_date + datetime.timedelta(seconds=random_seconds)
                
                # 创建日志记录
                log = AdminOperationLog.objects.create(
                    admin=admin,
                    operation=operation,
                    description=description,
                    ip_address=random_ip(),
                    target_type=target_type,
                    target_id=target_id,
                    status=status,
                    created_at=created_at
                )
                
                created_count += 1
                
                # 如果是失败状态，同时创建系统日志和安全日志
                if status == 'failed':
                    # 创建系统日志
                    SystemLog.objects.create(
                        log_type='error',
                        module='admin_management',
                        action=operation,
                        description=f'管理员操作失败：{description}',
                        ip_address=log.ip_address,
                        user_id=admin.id,
                        user_type='admin',
                        created_at=created_at,
                        hash_value=SM3Hasher.hash(description + str(created_at)),
                        prev_hash=None
                    )
                    
                    # 创建安全日志
                    SecurityLog.objects.create(
                        log_type='admin_operation_failed',
                        description=f'管理员操作失败：{description}',
                        ip_address=log.ip_address,
                        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        user_id=admin.id,
                        user_type='admin',
                        status='failed',
                        created_at=created_at,
                        hash_value=SM3Hasher.hash(description + str(created_at)),
                        prev_hash=None
                    )
                    
                    error_count += 1
                
                # 每5条日志输出一次进度
                if (i + 1) % 5 == 0:
                    self.stdout.write(f'已创建 {i + 1}/{count} 条日志...')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'创建日志时出错: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'成功创建 {created_count} 条管理员操作日志，其中 {error_count} 条错误日志'))
