"""
座位管理定时任务
"""
import logging
from django.utils import timezone
from celery import shared_task
from .utils import (
    handle_reservation_timeout,
    handle_reservation_auto_checkout,
    check_blacklist_status,
    verify_seat_operation_chain
)
from .models import Seat
from apps.authentication.utils import (
    clean_expired_sessions,
    unlock_accounts,
    restore_credit_scores
)

logger = logging.getLogger(__name__)

@shared_task
def process_reservation_timeout():
    """处理超时未签到的预约"""
    try:
        count = handle_reservation_timeout()
        logger.info(f"处理了 {count} 个超时未签到的预约")
        return count
    except Exception as e:
        logger.error(f"处理超时未签到的预约失败: {str(e)}")
        raise


@shared_task
def process_auto_checkout():
    """处理自动签退"""
    try:
        count = handle_reservation_auto_checkout()
        logger.info(f"处理了 {count} 个自动签退")
        return count
    except Exception as e:
        logger.error(f"处理自动签退失败: {str(e)}")
        raise


@shared_task
def process_blacklist_status():
    """处理黑名单状态"""
    try:
        count = check_blacklist_status()
        logger.info(f"处理了 {count} 个黑名单记录")
        return count
    except Exception as e:
        logger.error(f"处理黑名单状态失败: {str(e)}")
        raise


@shared_task
def process_expired_sessions():
    """处理过期会话"""
    try:
        count = clean_expired_sessions()
        logger.info(f"处理了 {count} 个过期会话")
        return count
    except Exception as e:
        logger.error(f"处理过期会话失败: {str(e)}")
        raise


@shared_task
def process_locked_accounts():
    """处理锁定账号"""
    try:
        count = unlock_accounts()
        logger.info(f"处理了 {count} 个锁定账号")
        return count
    except Exception as e:
        logger.error(f"处理锁定账号失败: {str(e)}")
        raise


@shared_task
def process_credit_score_restoration():
    """处理信誉分恢复"""
    try:
        count = restore_credit_scores()
        logger.info(f"处理了 {count} 个信誉分恢复")
        return count
    except Exception as e:
        logger.error(f"处理信誉分恢复失败: {str(e)}")
        raise


@shared_task
def verify_seat_operations():
    """验证座位操作记录链"""
    try:
        seats = Seat.objects.all()
        invalid_seats = []
        
        for seat in seats:
            is_valid, error = verify_seat_operation_chain(seat)
            if not is_valid:
                invalid_seats.append({
                    'seat_id': seat.id,
                    'seat_number': seat.seat_number,
                    'room': seat.room.name,
                    'error': error
                })
        
        if invalid_seats:
            logger.error(f"发现 {len(invalid_seats)} 个座位的操作记录链无效: {invalid_seats}")
        else:
            logger.info(f"验证了 {seats.count()} 个座位的操作记录链，全部有效")
        
        return len(invalid_seats)
    except Exception as e:
        logger.error(f"验证座位操作记录链失败: {str(e)}")
        raise
