<template>
  <div v-if="message" class="error-message" :class="type">
    <el-alert
      :title="title"
      :description="message"
      :type="type"
      :closable="closable"
      :show-icon="showIcon"
      @close="$emit('close')"
    />
  </div>
</template>

<script>
export default {
  name: "ErrorMessage",
  props: {
    title: {
      type: String,
      default: "错误",
    },
    message: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "error",
      validator: (value) =>
        ["error", "warning", "info", "success"].includes(value),
    },
    closable: {
      type: Boolean,
      default: true,
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["close"],
};
</script>

<style lang="scss" scoped>
.error-message {
  margin-bottom: 20px;
}
</style>
