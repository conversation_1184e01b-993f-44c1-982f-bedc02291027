from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器并注册视图集
router = DefaultRouter()
router.register(r'admins', views.AdminViewSet)
router.register(r'configs', views.SystemConfigViewSet)
router.register(r'logs', views.AdminOperationLogViewSet, basename='admin-log')

urlpatterns = [
    # 管理员登录
    path('login/', views.AdminLoginView.as_view(), name='admin-login'),

    # 管理员登出
    path('logout/', views.AdminLogoutView.as_view(), name='admin-logout'),

    # 包含路由器生成的URL
    path('', include(router.urls)),
]
