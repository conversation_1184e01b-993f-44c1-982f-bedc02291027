/**
 * 混合加密方案工具类
 * 提供前后端混合加密通信功能
 */
import { SM2Crypto, SM3Hasher, SM4Crypto } from "./crypto";
import axios from "axios";

/**
 * 加密工具类
 */
const CryptoUtils = {
  /**
   * 获取服务器SM2公钥
   * @returns {Promise<string>} 服务器公钥
   */
  async getServerPublicKey() {
    try {
      // 从API获取服务器公钥，避免硬编码
      const response = await axios.get("/api/v1/crypto/server-public-key");
      return response.data.publicKey;
    } catch (error) {
      console.error("获取服务器公钥失败:", error);
      throw new Error("获取服务器公钥失败");
    }
  },

  /**
   * 加密请求数据
   * @param {Object|string} data - 原始数据
   * @returns {Promise<Object>} - 加密后的数据包
   */
  async encryptRequest(data) {
    try {
      // 获取服务器公钥
      const serverPublicKey = await this.getServerPublicKey();

      // 生成随机SM4密钥
      const sm4Key = SM4Crypto.generateKey();

      // 如果是对象，转换为JSON字符串
      const dataStr = typeof data === "object" ? JSON.stringify(data) : data;

      // 使用SM4加密数据
      const encryptResult = SM4Crypto.encrypt(sm4Key, dataStr);

      // 使用SM2加密SM4密钥
      const encryptedKey = SM2Crypto.encrypt(serverPublicKey, sm4Key);

      // 返回加密包
      return {
        encrypted_data: encryptResult.ciphertext,
        encrypted_key: encryptedKey,
        iv: encryptResult.iv,
      };
    } catch (error) {
      console.error("加密请求数据失败:", error);
      throw new Error("加密请求数据失败");
    }
  },

  /**
   * 解密响应数据
   * @param {Object} encryptedPackage - 加密包
   * @param {string} privateKey - 客户端SM2私钥
   * @returns {Object|string} - 解密后的数据
   */
  decryptResponse(encryptedPackage, privateKey) {
    try {
      // 提取加密数据
      const { encrypted_data, encrypted_key, iv } = encryptedPackage;

      if (!encrypted_data || !encrypted_key || !iv) {
        throw new Error("加密包缺少必要字段");
      }

      // 使用SM2解密SM4密钥
      const sm4Key = SM2Crypto.decrypt(privateKey, encrypted_key);

      // 使用SM4解密数据
      const decryptedData = SM4Crypto.decrypt(sm4Key, encrypted_data, iv);

      // 尝试解析JSON
      try {
        return JSON.parse(decryptedData);
      } catch (e) {
        return decryptedData;
      }
    } catch (error) {
      console.error("解密响应数据失败:", error);
      throw new Error("解密响应数据失败");
    }
  },

  /**
   * 生成SM2密钥对
   * @returns {Object} - 包含公钥和私钥的对象
   */
  generateSM2KeyPair() {
    return SM2Crypto.generateKeyPair();
  },

  /**
   * 安全存储SM2私钥
   * @param {string} privateKey - SM2私钥
   * @param {string} password - 保护密码
   */
  storePrivateKey(privateKey, password) {
    // 使用密码加密私钥后存储
    const encryptedPrivateKey = SM4Crypto.encrypt(
      this.deriveKeyFromPassword(password),
      privateKey
    ).ciphertext;
    localStorage.setItem("sm2_private_key", encryptedPrivateKey);
  },

  /**
   * 获取存储的SM2私钥
   * @param {string} password - 保护密码
   * @returns {string} - SM2私钥
   */
  getStoredPrivateKey(password) {
    try {
      const encryptedPrivateKey = localStorage.getItem("sm2_private_key");
      if (!encryptedPrivateKey) {
        throw new Error("未找到存储的私钥");
      }

      // 使用密码解密私钥
      return SM4Crypto.decrypt(
        this.deriveKeyFromPassword(password),
        encryptedPrivateKey
      );
    } catch (error) {
      console.error("获取存储的私钥失败:", error);
      throw new Error("获取存储的私钥失败");
    }
  },

  /**
   * 从密码派生密钥
   * @param {string} password - 用户密码
   * @returns {string} - 派生的密钥
   */
  deriveKeyFromPassword(password) {
    // 简化实现，实际应使用PBKDF2等密钥派生函数
    return SM3Hasher.hash(password).substring(0, 32);
  },

  /**
   * 使用SM3哈希算法和盐值对密码进行哈希处理
   * @param {string} password - 密码
   * @param {string} salt - 盐值，如果为null则随机生成
   * @param {number} iterations - 迭代次数
   * @returns {Object} - 包含哈希值、盐值和迭代次数的对象
   */
  hashPassword(password, salt = null, iterations = 10000) {
    return SM3Hasher.hashWithSalt(password, salt, iterations);
  },

  /**
   * 验证密码是否匹配哈希值
   * @param {string} password - 密码
   * @param {string} hashValue - 哈希值(十六进制字符串)
   * @param {string} salt - 盐值(十六进制字符串)
   * @param {number} iterations - 迭代次数
   * @returns {boolean} - 是否匹配
   */
  verifyPassword(password, hashValue, salt, iterations = 10000) {
    return SM3Hasher.verify(password, hashValue, salt, iterations);
  },

  /**
   * 使用SM2签名数据
   * @param {Object|string} data - 待签名数据
   * @param {string} privateKey - SM2私钥(十六进制字符串)
   * @returns {string} - 签名(十六进制字符串)
   */
  signData(data, privateKey) {
    // 如果是对象，转换为JSON字符串
    const dataStr = typeof data === "object" ? JSON.stringify(data) : data;
    return SM2Crypto.sign(privateKey, dataStr);
  },

  /**
   * 验证SM2签名
   * @param {Object|string} data - 原始数据
   * @param {string} signature - 签名(十六进制字符串)
   * @param {string} publicKey - SM2公钥(十六进制字符串)
   * @returns {boolean} - 验证结果
   */
  verifySignature(data, signature, publicKey) {
    // 如果是对象，转换为JSON字符串
    const dataStr = typeof data === "object" ? JSON.stringify(data) : data;
    return SM2Crypto.verify(publicKey, dataStr, signature);
  },
};

export default CryptoUtils;
