# 图书馆自习室管理系统 - 数据库设计

## 一、数据库总体设计

### 1.1 数据库选型

本系统采用MySQL 8.0作为关系型数据库管理系统，Redis 6.x作为缓存和消息队列系统。选择MySQL的理由：

- 高性能：优秀的查询性能和事务处理能力
- 稳定性：成熟稳定的关系型数据库，有丰富的生产环境案例
- 安全特性：提供完善的数据安全机制
- 兼容性：与Django ORM良好兼容
- 易于维护：管理工具丰富，运维成本较低

### 1.2 数据库架构

本系统数据库架构如下：

```
┌───────────────┐      ┌───────────────┐
│ 应用服务器     │      │ MySQL主库     │
├───────────────┤      ├───────────────┤
│ Django ORM    │─────►│ 核心业务数据   │
└───────────────┘      └───────────────┘
       │                       │
       │                       │
       ▼                       ▼
┌───────────────┐      ┌───────────────┐
│ Redis服务     │      │ MySQL从库     │
├───────────────┤      ├───────────────┤
│ 缓存/消息队列  │      │ 读取分离/备份  │
└───────────────┘      └───────────────┘
```

- MySQL主库：处理所有写操作和核心读操作
- MySQL从库：通过主从复制提供读取分离和数据备份
- 本地缓存（django自带）：提供会话存储、缓存和消息队列功能

## 二、核心表结构设计  数据库名称 seat_management

### 2.1 用户相关表

#### 2.1.1 用户表（user）

存储用户基本信息和认证信息。

```sql
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `student_id` binary(64) NOT NULL COMMENT 'SM4加密的学号',
  `student_id_hash` varchar(64) NOT NULL COMMENT 'SM3哈希的学号(用于索引)',
  `password` varchar(64) NOT NULL COMMENT 'SM3哈希的密码',
  `salt` varchar(32) DEFAULT NULL COMMENT '密码盐值',
  `iterations` int DEFAULT 10000 COMMENT '密码哈希迭代次数',
  `public_key` text DEFAULT NULL COMMENT 'SM2公钥',
  `public_key_expires` datetime DEFAULT NULL COMMENT '公钥过期时间',
  `email` binary(128) DEFAULT NULL COMMENT 'SM4加密的邮箱',
  `phone` binary(64) DEFAULT NULL COMMENT 'SM4加密的手机号',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active/disabled/blacklisted)',
  `credit_score` int NOT NULL DEFAULT 100 COMMENT '信誉分',
  `login_attempts` int NOT NULL DEFAULT 0 COMMENT '登录失败次数',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_id_hash` (`student_id_hash`),
  KEY `idx_status` (`status`),
  KEY `idx_credit_score` (`credit_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2.1.2 用户会话表（user_session）

跟踪用户会话和令牌信息。

```sql
CREATE TABLE `user_session` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `token` varchar(64) NOT NULL COMMENT 'JWT令牌ID',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text NOT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否活跃',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`token`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `fk_session_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';
```

#### 2.1.3 管理员表（admin）

存储管理员信息。

```sql
CREATE TABLE `admin` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(64) NOT NULL COMMENT 'SM3哈希的密码',
  `salt` varchar(32) DEFAULT NULL COMMENT '密码盐值',
  `iterations` int DEFAULT 10000 COMMENT '密码哈希迭代次数',
  `role` varchar(20) NOT NULL DEFAULT 'admin' COMMENT '角色(admin/super_admin)',
  `auth_key` varchar(64) DEFAULT NULL COMMENT 'TOTP认证密钥(SM4加密)',
  `email` binary(128) DEFAULT NULL COMMENT 'SM4加密的邮箱',
  `phone` binary(64) DEFAULT NULL COMMENT 'SM4加密的手机号',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active/disabled)',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
```

#### 2.1.4 信誉分记录表（credit_record）

记录用户信誉分变动历史。

```sql
CREATE TABLE `credit_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `delta` int NOT NULL COMMENT '变动分值',
  `reason` varchar(255) NOT NULL COMMENT '变动原因',
  `score_after` int NOT NULL COMMENT '变动后分数',
  `operator_id` bigint DEFAULT NULL COMMENT '操作员ID（管理员操作）',
  `operator_type` varchar(10) DEFAULT 'system' COMMENT '操作员类型(system/admin)',
  `related_entity` varchar(20) DEFAULT NULL COMMENT '相关实体(reservation/check_in)',
  `related_id` bigint DEFAULT NULL COMMENT '相关实体ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_credit_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信誉分记录表';
```

### 2.2 座位相关表

#### 2.2.1 自习室表（room）

存储自习室基本信息。

```sql
CREATE TABLE `room` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '自习室名称',
  `building` varchar(50) NOT NULL COMMENT '所在建筑',
  `floor` int NOT NULL COMMENT '楼层',
  `capacity` int NOT NULL COMMENT '座位容量',
  `status` varchar(20) NOT NULL DEFAULT 'open' COMMENT '状态(open/closed/maintenance)',
  `open_time` time NOT NULL DEFAULT '08:00:00' COMMENT '开放时间',
  `close_time` time NOT NULL DEFAULT '22:00:00' COMMENT '关闭时间',
  `description` text DEFAULT NULL COMMENT '说明',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_building_floor` (`building`, `floor`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自习室表';
```

#### 2.2.2 座位表（seat）

存储座位信息和状态。

```sql
CREATE TABLE `seat` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` bigint NOT NULL COMMENT '自习室ID',
  `seat_number` varchar(10) NOT NULL COMMENT '座位号',
  `status` varchar(10) NOT NULL DEFAULT '空闲' COMMENT '状态(空闲/占用/维护/禁用)',
  `position_x` int NOT NULL DEFAULT 0 COMMENT 'X坐标',
  `position_y` int NOT NULL DEFAULT 0 COMMENT 'Y坐标',
  `is_power_outlet` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有电源插座',
  `is_window_seat` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否靠窗座位',
  `amenities` varchar(255) DEFAULT NULL COMMENT '其他设施',
  `last_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_seat` (`room_id`,`seat_number`),
  KEY `idx_status` (`status`),
  KEY `idx_room` (`room_id`),
  CONSTRAINT `fk_seat_room` FOREIGN KEY (`room_id`) REFERENCES `room` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='座位表';
```

#### 2.2.3 预约表（reservation）

存储座位预约信息。

```sql
CREATE TABLE `reservation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `seat_id` bigint NOT NULL COMMENT '座位ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` varchar(20) NOT NULL DEFAULT '预约中' COMMENT '状态(预约中/已签到/已完成/已取消/未签到/已过期)',
  `encrypted_data` binary(255) NOT NULL COMMENT 'SM4加密的预约数据',
  `encryption_key_id` varchar(36) DEFAULT NULL COMMENT '加密密钥ID',
  `check_in_time` datetime DEFAULT NULL COMMENT '签到时间',
  `check_out_time` datetime DEFAULT NULL COMMENT '签退时间',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_start` (`user_id`,`start_time`),
  KEY `idx_seat_start` (`seat_id`,`start_time`),
  KEY `idx_status_start` (`status`,`start_time`),
  KEY `idx_start_end` (`start_time`,`end_time`),
  CONSTRAINT `fk_reservation_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_reservation_seat` FOREIGN KEY (`seat_id`) REFERENCES `seat` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预约表';
```

#### 2.2.4 违规记录表（violation）

记录用户违规行为。

```sql
CREATE TABLE `violation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `reservation_id` bigint DEFAULT NULL COMMENT '预约ID',
  `type` varchar(20) NOT NULL COMMENT '违规类型(未签到/恶意占座/损坏设施/扰乱秩序)',
  `description` text NOT NULL COMMENT '详细描述',
  `penalty` int NOT NULL DEFAULT 0 COMMENT '扣分数量',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active/appealed/forgiven)',
  `appeal_reason` text DEFAULT NULL COMMENT '申诉理由',
  `admin_id` bigint DEFAULT NULL COMMENT '处理管理员ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_reservation_id` (`reservation_id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_violation_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_violation_reservation` FOREIGN KEY (`reservation_id`) REFERENCES `reservation` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='违规记录表';
```

### 2.3 日志相关表

#### 2.3.1 系统日志表（system_log）

记录系统操作日志，使用SM3哈希链保证完整性。

```sql
CREATE TABLE `system_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `admin_id` bigint DEFAULT NULL COMMENT '管理员ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `details` text DEFAULT NULL COMMENT '详细信息',
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `prev_hash` varchar(64) DEFAULT NULL COMMENT '前一条日志的哈希值',
  `curr_hash` varchar(64) NOT NULL COMMENT '当前日志的哈希值',
  PRIMARY KEY (`id`),
  KEY `idx_action` (`action`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';
```

#### 2.3.2 安全审计日志表（security_log）

记录安全相关事件。

```sql
CREATE TABLE `security_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型(登录失败/密钥轮换/证书操作/日志篡改)',
  `severity` varchar(20) NOT NULL COMMENT '严重程度(info/warning/critical)',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `admin_id` bigint DEFAULT NULL COMMENT '管理员ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `description` text NOT NULL COMMENT '事件描述',
  `additional_data` text DEFAULT NULL COMMENT '额外数据(JSON)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全审计日志表';
```

### 2.4 系统配置相关表

#### 2.4.1 系统配置表（system_config）

存储系统配置参数。

```sql
CREATE TABLE `system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `key` varchar(50) NOT NULL COMMENT '配置键',
  `value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加密',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

#### 2.4.2 SM4密钥表（sm4_key）

存储SM4密钥信息，用于密钥轮换。

```sql
CREATE TABLE `sm4_key` (
  `id` varchar(36) NOT NULL COMMENT '密钥ID',
  `encrypted_key` binary(64) NOT NULL COMMENT 'SM2加密的SM4密钥',
  `active` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为当前活跃密钥',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `activated_at` datetime DEFAULT NULL COMMENT '激活时间',
  `deactivated_at` datetime DEFAULT NULL COMMENT '停用时间',
  PRIMARY KEY (`id`),
  KEY `idx_active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SM4密钥表';
```

#### 2.4.3 信誉分规则表（credit_rule）

存储信誉分加减规则。

```sql
CREATE TABLE `credit_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` varchar(20) NOT NULL COMMENT '规则类型(加分/扣分)',
  `action` varchar(50) NOT NULL COMMENT '触发动作',
  `description` varchar(255) NOT NULL COMMENT '规则描述',
  `delta` int NOT NULL COMMENT '分值变化',
  `max_times_per_day` int DEFAULT NULL COMMENT '每日最大次数',
  `active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信誉分规则表';
```

## 三、索引设计与优化

### 3.1 索引策略

1. **主键索引**：每个表都使用自增`id`作为主键，提供高效的行访问。

2. **唯一索引**：对需要唯一性约束的字段创建唯一索引，如：
   - `user.student_id_hash` - 确保学号唯一性
   - `room.name` + `room.building` - 确保自习室名称在同一建筑内唯一
   - `seat.room_id` + `seat.seat_number` - 确保座位号在同一自习室内唯一

3. **外键索引**：所有外键字段都创建了索引，提高关联查询效率。

4. **复合索引**：根据常见查询模式创建复合索引，如：
   - `reservation.user_id` + `reservation.start_time` - 查询用户特定时间段的预约
   - `reservation.seat_id` + `reservation.start_time` - 查询座位是否被预约
   - `reservation.start_time` + `reservation.end_time` - 时间冲突检测查询

5. **哈希索引**：为加密字段创建哈希索引，提高查询效率，如：
   - `user.student_id_hash` - 用于快速查找用户

### 3.2 索引优化

1. **选择性索引**：
   - 为高选择性字段创建索引，如用户ID
   - 避免对低选择性字段（如状态字段）单独建立索引

2. **覆盖索引**：
   - 设计索引时考虑覆盖常见查询，减少回表操作
   - 例如，预约查询可能经常需要用户ID和时间段，因此设计了复合索引

3. **索引列顺序**：
   - 在复合索引中，把等值查询的列放在前面，范围查询的列放在后面
   - 例如，在`idx_user_start`中，先是用户ID(等值)，然后是开始时间(范围)

4. **前缀索引**：
   - 对于长文本字段，只索引前缀部分，减少索引大小
   - 例如，可以对email字段只索引前20个字符

## 四、数据安全设计

### 4.1 加密存储方案

1. **敏感字段加密**：
   - 学号、邮箱、手机号等个人信息使用SM4加密存储
   - 加密的字段使用binary类型存储，避免编码问题
   - 创建哈希索引（如student_id_hash）用于查询

2. **密码安全**：
   - 密码使用SM3哈希+盐值+多次迭代存储
   - 不存储明文密码，仅存储哈希结果
   - 验证时，对输入密码执行相同的哈希操作并比对

3. **密钥管理**：
   - SM4密钥定期轮换，轮换周期为30天
   - 旧密钥保留一定时间，确保已加密数据仍可解密
   - 密钥本身使用SM2公钥加密存储

### 4.2 数据完整性保障

1. **哈希链**：
   - 系统日志使用SM3哈希链技术，确保日志不被篡改
   - 每条日志记录都包含前一条记录的哈希值
   - 定期验证哈希链完整性，检测篡改

2. **数据签名**：
   - 关键业务数据（如预约记录）使用SM3签名
   - 验证数据完整性，防止未授权修改
   - 提供数据来源验证

3. **约束与触发器**：
   - 使用数据库约束确保数据一致性
   - 使用触发器记录敏感数据变更
   - 实现自动审计追踪

### 4.3 访问控制与审计

1. **角色权限**：
   - 数据库用户设置最小权限原则
   - 应用程序使用只读账号进行查询
   - 仅特定API有写入权限

2. **操作审计**：
   - 记录所有数据库敏感操作
   - 审计日志不可被应用程序删除
   - 定期审查异常操作

3. **数据隔离**：
   - 敏感配置与业务数据分离存储
   - 加密密钥与加密数据分开管理
   - 防止单点泄露导致全局风险

## 五、数据迁移与备份策略

### 5.1 数据迁移

1. **迁移脚本**：
   - 使用Django migrations管理数据库结构变更
   - 每次结构变更都有对应的回滚方案
   - 确保迁移过程可重复执行

2. **数据转换**：
   - 旧数据迁移时进行加密转换
   - 维护数据一致性和完整性
   - 迁移过程中记录详细日志

### 5.2 备份策略

1. **定时备份**：
   - 每日全量备份
   - 每小时增量备份
   - 备份文件SM4加密存储

2. **多地备份**：
   - 本地备份 + 远程备份
   - 至少保留7天的备份历史
   - 定期备份恢复测试

3. **恢复流程**：
   - 明确的数据恢复流程文档
   - 定期恢复演练
   - 设置恢复时间目标(RTO)和恢复点目标(RPO)

## 六、缓存设计

### 6.1 缓存策略

1. **热点数据缓存**：
   - 活跃自习室和座位状态
   - 用户最近的预约数据
   - 系统配置信息

2. **缓存键设计**：
   - 使用有意义的前缀，如`seat:status:1`
   - 设置合理的过期时间
   - 避免缓存雪崩和缓存穿透

3. **失效策略**：
   - 使用LRU（最近最少使用）策略
   - 关键数据设置硬过期时间

### 6.2其他用

1. **会话存储**：
   - 存储用户会话信息
   - JWT令牌黑名单管理
   - 临时授权码存储

2. **消息队列**：
   - 异步任务处理
   - 系统通知发送
   - 日志聚合处理

3. **实时数据**：
   - WebSocket连接管理
   - 座位状态实时更新
   - 系统监控数据

## 七、性能优化建议

1. **查询优化**：
   - 使用EXPLAIN分析SQL执行计划
   - 优化复杂查询，避免全表扫描
   - 合理使用JOIN和子查询

2. **索引优化**：
   - 定期分析索引使用情况
   - 移除未使用的索引
   - 优化索引覆盖常见查询

3. **连接池优化**：
   - 设置合理的连接池大小
   - 监控连接状态和等待时间
   - 处理连接泄漏问题

4. **缓存优化**：
   - 监控缓存命中率
   - 优化缓存策略
   - 避免缓存与数据库不一致

5. **定期维护**：
   - 数据表碎片整理
   - 统计信息更新
   - 性能监控与调优 