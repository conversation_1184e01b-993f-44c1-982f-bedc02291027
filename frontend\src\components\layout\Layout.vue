<template>
  <div class="app-layout">
    <app-header />
    <div class="main-container">
      <!-- 开发模式：始终显示侧边栏 -->
      <app-sidebar />
      <div class="content-container">
        <router-view />
      </div>
    </div>
    <app-footer />
  </div>
</template>

<script>
import { computed } from "vue";
import { useStore } from "vuex";
import AppHeader from "./Header.vue";
import AppSidebar from "./Sidebar.vue";
import AppFooter from "./Footer.vue";

export default {
  name: "AppLayout",
  components: {
    AppHeader,
    AppSidebar,
    AppFooter,
  },
  setup() {
    const store = useStore();
    const isLoggedIn = computed(() => store.getters["user/isLoggedIn"]);

    return {
      isLoggedIn,
    };
  },
};
</script>

<style lang="scss" scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-container {
  display: flex;
  flex: 1;
}

.content-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}
</style>
