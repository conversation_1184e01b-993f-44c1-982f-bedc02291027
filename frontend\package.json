{"name": "seat-management-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.9.0", "core-js": "^3.8.3", "element-plus": "^2.9.10", "qrcodejs2": "^0.0.2", "sm-crypto": "^0.3.13", "vue": "^3.2.13", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/babel-plugin-jsx": "^1.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0"}}