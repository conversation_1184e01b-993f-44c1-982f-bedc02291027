from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.seat.models import Room, Seat
import datetime

class Command(BaseCommand):
    help = '创建图书馆自习室和座位数据'

    def add_arguments(self, parser):
        parser.add_argument('--force', action='store_true', help='强制重新创建数据（会删除现有数据）')

    def handle(self, *args, **options):
        force = options['force']
        
        # 检查是否已存在数据
        existing_rooms = Room.objects.count()
        if existing_rooms > 0 and not force:
            self.stdout.write(self.style.WARNING(f'已存在 {existing_rooms} 个自习室，使用 --force 参数强制重新创建'))
            return
        
        # 如果强制重新创建，先删除现有数据
        if force:
            self.stdout.write('删除现有数据...')
            Room.objects.all().delete()
        
        # 创建自习室
        self.stdout.write('开始创建自习室...')
        rooms = []
        
        # 图书馆共八层，1-6层为开放区域，7-8层不开放
        # 每层分东区西区，共两个自习室
        for floor in range(1, 9):
            for area in ['东区', '西区']:
                # 7-8层不开放
                status = 'open' if floor <= 6 else 'closed'
                
                # 每个自习室座位为6×6的布局
                capacity = 36
                
                room_name = f'{floor}楼{area}自习室'
                room = Room.objects.create(
                    name=room_name,
                    location=f'图书馆{floor}楼{area}',
                    floor=floor,
                    capacity=capacity,
                    open_time=datetime.time(8, 0),  # 8:00 AM
                    close_time=datetime.time(22, 0),  # 10:00 PM
                    status=status,
                    description=f'图书馆{floor}楼{area}自习室，{"开放" if status == "open" else "不开放"}区域'
                )
                rooms.append(room)
                self.stdout.write(f'  - 创建自习室: {room.name} (状态: {status})')
        
        # 为每个自习室创建座位
        self.stdout.write('开始创建座位...')
        for room in rooms:
            # 创建一个6×6的座位网格
            for row in range(1, 7):
                for col in range(1, 7):
                    seat_number = f'{row}-{col}'
                    
                    # 每个自习室座位靠边每隔一位有电源插座
                    # 靠边的座位：第1行、第6行、第1列、第6列
                    is_edge_seat = row == 1 or row == 6 or col == 1 or col == 6
                    
                    # 每隔一位有电源插座
                    is_power_outlet = is_edge_seat and ((row + col) % 2 == 0)
                    
                    # 靠窗的座位：第1行和第6行
                    is_window_seat = row == 1 or row == 6
                    
                    # 创建座位
                    Seat.objects.create(
                        room=room,
                        seat_number=seat_number,
                        row=row,
                        column=col,
                        status='available' if room.status == 'open' else 'disabled',
                        is_power_outlet=is_power_outlet,
                        is_window_seat=is_window_seat
                    )
            
            self.stdout.write(f'  - 为 {room.name} 创建了 36 个座位')
        
        total_rooms = len(rooms)
        total_seats = total_rooms * 36
        self.stdout.write(self.style.SUCCESS(f'成功创建 {total_rooms} 个自习室和 {total_seats} 个座位'))
