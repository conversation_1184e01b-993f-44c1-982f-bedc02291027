<template>
  <div class="operation-records-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>操作记录</h2>
        </div>
      </template>

      <el-table :data="records" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="operation_type" label="操作类型" width="120" />
        <el-table-column prop="operation_time" label="操作时间" width="180" />
        <el-table-column prop="operation_content" label="操作内容" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";

export default {
  name: "OperationRecords",
  setup() {
    const records = ref([]);
    const loading = ref(true);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);

    // 模拟数据
    const mockData = [
      {
        id: 1,
        operation_type: "预约座位",
        operation_time: "2023-10-15 09:30:45",
        operation_content: "预约了A区101号座位",
        status: "成功",
      },
      {
        id: 2,
        operation_type: "取消预约",
        operation_time: "2023-10-16 14:20:30",
        operation_content: "取消了A区105号座位的预约",
        status: "成功",
      },
      {
        id: 3,
        operation_type: "签到",
        operation_time: "2023-10-17 08:05:12",
        operation_content: "在B区203号座位签到",
        status: "成功",
      },
      {
        id: 4,
        operation_type: "签退",
        operation_time: "2023-10-17 12:30:00",
        operation_content: "从B区203号座位签退",
        status: "成功",
      },
      {
        id: 5,
        operation_type: "预约座位",
        operation_time: "2023-10-18 16:45:22",
        operation_content: "预约了C区301号座位",
        status: "失败",
      },
    ];

    const fetchRecords = () => {
      loading.value = true;
      // 模拟API请求
      setTimeout(() => {
        records.value = mockData;
        total.value = mockData.length;
        loading.value = false;
      }, 500);
    };

    const handleSizeChange = (val) => {
      pageSize.value = val;
      fetchRecords();
    };

    const handleCurrentChange = (val) => {
      currentPage.value = val;
      fetchRecords();
    };

    onMounted(() => {
      fetchRecords();
    });

    return {
      records,
      loading,
      currentPage,
      pageSize,
      total,
      handleSizeChange,
      handleCurrentChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.operation-records-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
