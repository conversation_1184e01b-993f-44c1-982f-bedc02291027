<template>
  <div class="seat-reservation">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
        <h2>座位预约</h2>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <template v-else>
      <el-steps
        :active="currentStep"
        finish-status="success"
        class="reservation-steps"
      >
        <el-step title="选择座位" />
        <el-step title="选择时间" />
        <el-step title="确认预约" />
        <el-step title="预约成功" />
      </el-steps>

      <!-- 步骤1：选择座位 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-card shadow="hover" class="seat-info-card">
          <template #header>
            <div class="card-header">
              <h3>座位信息</h3>
            </div>
          </template>

          <template v-if="seat">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="自习室">{{
                room?.name
              }}</el-descriptions-item>
              <el-descriptions-item label="位置">{{
                room?.location
              }}</el-descriptions-item>
              <el-descriptions-item label="座位编号">{{
                seat.seat_number
              }}</el-descriptions-item>
              <el-descriptions-item label="座位位置">
                {{ `${seat.row}排${seat.column}列` }}
              </el-descriptions-item>
              <el-descriptions-item label="设施">
                <el-tag
                  v-if="seat.is_power_outlet"
                  type="success"
                  effect="plain"
                  >有电源</el-tag
                >
                <el-tag v-if="seat.is_window_seat" type="success" effect="plain"
                  >靠窗</el-tag
                >
                <span v-if="!seat.is_power_outlet && !seat.is_window_seat"
                  >无特殊设施</span
                >
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getSeatStatusType(seat.status)">
                  {{ getSeatStatusText(seat.status) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </template>

          <template v-else>
            <el-empty description="未选择座位">
              <el-button type="primary" @click="$router.push('/seat/rooms')"
                >去选择座位</el-button
              >
            </el-empty>
          </template>

          <div v-if="seat" class="step-actions">
            <el-button type="primary" @click="nextStep">下一步</el-button>
            <el-button @click="$router.push('/seat/rooms')">重新选择</el-button>
          </div>
        </el-card>
      </div>

      <!-- 步骤2：选择时间 -->
      <div v-else-if="currentStep === 1" class="step-content">
        <el-card shadow="hover" class="time-selection-card">
          <template #header>
            <div class="card-header">
              <h3>选择预约时间</h3>
              <div class="date-selector">
                <span>日期：</span>
                <el-date-picker
                  v-model="selectedDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  @change="loadTimeSlots"
                />
              </div>
            </div>
          </template>

          <div v-if="loadingTimeSlots" class="loading-time-slots">
            <el-skeleton :rows="5" animated />
          </div>

          <template v-else>
            <div class="time-slots-container">
              <div class="time-slots-header">
                <h4>可用时间段</h4>
                <p class="time-hint">
                  开放时间: {{ formatTime(room?.open_time) }} -
                  {{ formatTime(room?.close_time) }}
                </p>
              </div>

              <div v-if="timeSlots.length === 0" class="empty-time-slots">
                <el-empty description="当前日期没有可用的时间段" />
              </div>

              <div v-else class="time-slots">
                <div
                  v-for="(slot, index) in timeSlots"
                  :key="index"
                  class="time-slot"
                  :class="{
                    unavailable: !slot.is_available,
                    selected: isTimeSlotSelected(slot),
                  }"
                  @click="selectTimeSlot(slot)"
                >
                  <span class="time-range">{{ formatTimeSlot(slot) }}</span>
                  <el-tag v-if="slot.is_available" type="success" size="small"
                    >可用</el-tag
                  >
                  <el-tag v-else type="danger" size="small">已约</el-tag>
                </div>
              </div>

              <div class="selected-time-range">
                <h4>已选时间段</h4>
                <template v-if="selectedStartTime && selectedEndTime">
                  <p class="selected-time">
                    {{ formatDateTime(selectedStartTime) }} -
                    {{ formatDateTime(selectedEndTime) }}
                    <span class="duration">({{ calculateDuration() }})</span>
                  </p>
                </template>
                <p v-else class="no-selection">请选择时间段</p>
              </div>
            </div>

            <div class="step-actions">
              <el-button
                type="primary"
                @click="nextStep"
                :disabled="!canProceed"
                >下一步</el-button
              >
              <el-button @click="prevStep">上一步</el-button>
            </div>
          </template>
        </el-card>
      </div>

      <!-- 步骤3：确认预约 -->
      <div v-else-if="currentStep === 2" class="step-content">
        <el-card shadow="hover" class="confirm-card">
          <template #header>
            <div class="card-header">
              <h3>确认预约信息</h3>
            </div>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="自习室">{{
              room?.name
            }}</el-descriptions-item>
            <el-descriptions-item label="位置">{{
              room?.location
            }}</el-descriptions-item>
            <el-descriptions-item label="座位编号">{{
              seat?.seat_number
            }}</el-descriptions-item>
            <el-descriptions-item label="预约日期">{{
              selectedDate
            }}</el-descriptions-item>
            <el-descriptions-item label="预约时间">
              {{ formatDateTime(selectedStartTime) }} -
              {{ formatDateTime(selectedEndTime) }}
              <span class="duration">({{ calculateDuration() }})</span>
            </el-descriptions-item>
          </el-descriptions>

          <div class="reservation-notes">
            <h4>预约须知</h4>
            <ul>
              <li>
                预约成功后，请在预约开始时间后30分钟内完成签到，否则系统将自动取消预约。
              </li>
              <li>如需取消预约，请提前操作，避免影响信誉分。</li>
              <li>请遵守自习室规定，保持安静，不要占用他人座位。</li>
              <li>离开时请及时签退，方便他人使用。</li>
            </ul>
          </div>

          <div class="step-actions">
            <el-button
              type="primary"
              @click="submitReservation"
              :loading="submitting"
            >
              确认预约
            </el-button>
            <el-button @click="prevStep">上一步</el-button>
          </div>
        </el-card>
      </div>

      <!-- 步骤4：预约成功 -->
      <div v-else-if="currentStep === 3" class="step-content">
        <el-card shadow="hover" class="success-card">
          <el-result
            icon="success"
            title="预约成功"
            sub-title="您已成功预约座位"
          >
            <template #extra>
              <div class="reservation-details">
                <p>
                  <strong>预约编号：</strong>
                  {{ reservation?.id }}
                </p>
                <p>
                  <strong>自习室：</strong>
                  {{ room?.name }}
                </p>
                <p>
                  <strong>座位编号：</strong>
                  {{ seat?.seat_number }}
                </p>
                <p>
                  <strong>预约时间：</strong>
                  {{ formatDateTime(reservation?.start_time) }} -
                  {{ formatDateTime(reservation?.end_time) }}
                </p>
                <p>
                  <strong>签到码：</strong>
                  {{ reservation?.reservation_code }}
                </p>
              </div>

              <div class="action-buttons">
                <el-button
                  type="primary"
                  @click="$router.push('/user/reservations')"
                >
                  查看我的预约
                </el-button>
                <el-button @click="$router.push('/seat/rooms')"
                  >返回自习室列表</el-button
                >
              </div>
            </template>
          </el-result>
        </el-card>
      </div>
    </template>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";

export default {
  name: "SeatReservation",
  setup() {
    const store = useStore();
    const route = useRoute();
    const router = useRouter();

    const loading = ref(true);
    const loadingTimeSlots = ref(false);
    const submitting = ref(false);
    const currentStep = ref(0);

    const room = ref(null);
    const seat = ref(null);
    const timeSlots = ref([]);
    const reservation = ref(null);

    const selectedDate = ref("");
    const selectedStartTime = ref(null);
    const selectedEndTime = ref(null);

    // 是否可以进行下一步
    const canProceed = computed(() => {
      if (currentStep.value === 0) {
        return !!seat.value;
      } else if (currentStep.value === 1) {
        return !!selectedStartTime.value && !!selectedEndTime.value;
      }
      return true;
    });

    // 加载座位信息
    const loadSeatInfo = async () => {
      try {
        loading.value = true;

        const seatId = route.query.seatId;
        if (!seatId) {
          ElMessage.error("缺少座位ID参数");
          router.push("/seat/rooms");
          return;
        }

        // 加载座位信息
        const seatData = await store.dispatch("seat/getSeatById", seatId);
        seat.value = seatData;

        // 加载自习室信息
        const roomData = await store.dispatch(
          "seat/getRoomById",
          seat.value.room
        );
        room.value = roomData;

        // 设置默认日期
        if (route.query.date) {
          selectedDate.value = route.query.date;
        } else {
          selectedDate.value = formatDateForSelect(new Date());
        }

        // 加载时间段
        await loadTimeSlots();
      } catch (error) {
        ElMessage.error("加载座位信息失败");
        router.push("/seat/rooms");
      } finally {
        loading.value = false;
      }
    };

    // 加载时间段
    const loadTimeSlots = async () => {
      try {
        loadingTimeSlots.value = true;

        // 重置选择的时间
        selectedStartTime.value = null;
        selectedEndTime.value = null;

        // 加载时间段
        const timeSlotsData = await store.dispatch("seat/getSeatTimeSlots", {
          seatId: seat.value.id,
          date: selectedDate.value,
        });

        timeSlots.value = timeSlotsData.time_slots;
      } catch (error) {
        ElMessage.error("加载时间段失败");
      } finally {
        loadingTimeSlots.value = false;
      }
    };

    // 选择时间段
    const selectTimeSlot = (slot) => {
      if (!slot.is_available) return;

      const startTime = new Date(slot.start_time);
      const endTime = new Date(slot.end_time);

      // 如果没有选择开始时间，或者已经选择了开始和结束时间
      if (
        !selectedStartTime.value ||
        (selectedStartTime.value && selectedEndTime.value)
      ) {
        selectedStartTime.value = startTime;
        selectedEndTime.value = endTime;
      }
      // 如果已经选择了开始时间，但没有选择结束时间
      else if (selectedStartTime.value && !selectedEndTime.value) {
        // 如果选择的时间早于已选的开始时间，则作为新的开始时间
        if (startTime < selectedStartTime.value) {
          selectedStartTime.value = startTime;
        }
        // 如果选择的时间晚于已选的开始时间，则作为结束时间
        else if (startTime > selectedStartTime.value) {
          // 检查中间是否有不可用的时间段
          const hasUnavailableSlot = timeSlots.value.some((s) => {
            const slotStart = new Date(s.start_time);
            const slotEnd = new Date(s.end_time);
            return (
              !s.is_available &&
              slotStart >= selectedStartTime.value &&
              slotEnd <= endTime
            );
          });

          if (hasUnavailableSlot) {
            ElMessage.warning("所选时间段中包含已被预约的时间");
            return;
          }

          selectedEndTime.value = endTime;
        }
      }
    };

    // 判断时间段是否被选中
    const isTimeSlotSelected = (slot) => {
      if (!selectedStartTime.value) return false;

      const slotStart = new Date(slot.start_time);
      const slotEnd = new Date(slot.end_time);

      if (selectedEndTime.value) {
        return (
          slotStart >= selectedStartTime.value &&
          slotEnd <= selectedEndTime.value
        );
      } else {
        return slotStart.getTime() === selectedStartTime.value.getTime();
      }
    };

    // 计算时长
    const calculateDuration = () => {
      if (!selectedStartTime.value || !selectedEndTime.value) return "";

      const diffMs = selectedEndTime.value - selectedStartTime.value;
      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      return `${diffHrs}小时${diffMins}分钟`;
    };

    // 禁用日期
    const disabledDate = (date) => {
      // 禁用过去的日期和7天后的日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const maxDate = new Date();
      maxDate.setDate(maxDate.getDate() + 6);
      maxDate.setHours(23, 59, 59, 999);

      return date < today || date > maxDate;
    };

    // 提交预约
    const submitReservation = async () => {
      try {
        submitting.value = true;

        // 提交预约
        const reservationData = await store.dispatch("seat/createReservation", {
          seatId: seat.value.id,
          startTime: selectedStartTime.value.toISOString(),
          endTime: selectedEndTime.value.toISOString(),
        });

        reservation.value = reservationData;

        // 进入下一步
        currentStep.value = 3;

        ElMessage.success("预约成功");
      } catch (error) {
        ElMessage.error(error.message || "预约失败");
      } finally {
        submitting.value = false;
      }
    };

    // 下一步
    const nextStep = () => {
      if (!canProceed.value) return;
      currentStep.value++;
    };

    // 上一步
    const prevStep = () => {
      currentStep.value--;
    };

    // 获取座位状态类型
    const getSeatStatusType = (status) => {
      switch (status) {
        case "available":
          return "success";
        case "occupied":
          return "danger";
        case "disabled":
          return "info";
        default:
          return "info";
      }
    };

    // 获取座位状态文本
    const getSeatStatusText = (status) => {
      switch (status) {
        case "available":
          return "可用";
        case "occupied":
          return "已占用";
        case "disabled":
          return "禁用";
        default:
          return "未知状态";
      }
    };

    // 格式化时间
    const formatTime = (timeString) => {
      if (!timeString) return "";

      // 时间格式为 "HH:MM:SS"，只显示 "HH:MM"
      return timeString.substring(0, 5);
    };

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return "";

      const date = new Date(dateTime);
      return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
    };

    // 格式化时间段
    const formatTimeSlot = (slot) => {
      const start = new Date(slot.start_time);
      const end = new Date(slot.end_time);

      return `${padZero(start.getHours())}:${padZero(
        start.getMinutes()
      )} - ${padZero(end.getHours())}:${padZero(end.getMinutes())}`;
    };

    // 格式化日期（用于选择器值）
    function formatDateForSelect(date) {
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
        date.getDate()
      )}`;
    }

    // 补零
    function padZero(num) {
      return num < 10 ? `0${num}` : num;
    }

    // 监听日期变化
    watch(selectedDate, () => {
      loadTimeSlots();
    });

    onMounted(() => {
      loadSeatInfo();
    });

    return {
      loading,
      loadingTimeSlots,
      submitting,
      currentStep,
      room,
      seat,
      timeSlots,
      reservation,
      selectedDate,
      selectedStartTime,
      selectedEndTime,
      canProceed,
      loadTimeSlots,
      selectTimeSlot,
      isTimeSlotSelected,
      calculateDuration,
      disabledDate,
      submitReservation,
      nextStep,
      prevStep,
      getSeatStatusType,
      getSeatStatusText,
      formatTime,
      formatDateTime,
      formatTimeSlot,
      ArrowLeft,
    };
  },
};
</script>

<style lang="scss" scoped>
.seat-reservation {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    h2 {
      margin: 0;
    }
  }
}

.reservation-steps {
  margin-bottom: 30px;
}

.loading-container {
  padding: 40px 0;
}

.step-content {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
  }

  .date-selector {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.step-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.loading-time-slots {
  padding: 20px 0;
}

.time-slots-container {
  .time-slots-header {
    margin-bottom: 15px;

    h4 {
      margin: 0 0 5px 0;
    }

    .time-hint {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .empty-time-slots {
    padding: 20px 0;
  }

  .time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 20px;

    .time-slot {
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover:not(.unavailable) {
        border-color: #409eff;
        background-color: #ecf5ff;
      }

      &.selected {
        border-color: #409eff;
        background-color: #ecf5ff;
      }

      &.unavailable {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .time-range {
        font-size: 14px;
      }
    }
  }

  .selected-time-range {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    h4 {
      margin: 0 0 10px 0;
    }

    .selected-time {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      color: #409eff;

      .duration {
        margin-left: 10px;
        font-size: 14px;
        font-weight: normal;
        color: #606266;
      }
    }

    .no-selection {
      margin: 0;
      color: #909399;
    }
  }
}

.reservation-notes {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  h4 {
    margin: 0 0 10px 0;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.reservation-details {
  text-align: left;
  margin-bottom: 20px;

  p {
    margin: 5px 0;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.duration {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
}
</style>
