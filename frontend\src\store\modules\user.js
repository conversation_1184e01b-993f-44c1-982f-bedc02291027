import api from "@/api/user";

const state = {
  token: localStorage.getItem("token") || "",
  userInfo: JSON.parse(localStorage.getItem("userInfo") || "{}"),
  sm2Challenge: null,
  creditRecords: [],
};

const getters = {
  isLoggedIn: (state) => !!state.token,
  userInfo: (state) => state.userInfo,
  token: (state) => state.token,
  sm2Challenge: (state) => state.sm2Challenge,
  creditRecords: (state) => state.creditRecords,
};

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token;
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
  },
  SET_SM2_CHALLENGE(state, challenge) {
    state.sm2Challenge = challenge;
  },
  SET_CREDIT_RECORDS(state, records) {
    state.creditRecords = records;
  },
  CLEAR_USER_DATA(state) {
    state.token = "";
    state.userInfo = {};
    state.sm2Challenge = null;
    state.creditRecords = [];
  },
};

const actions = {
  // 用户登录
  async login({ commit, dispatch }, { studentId, password }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.login(studentId, password);
      const { token, user } = response.data;

      // 保存令牌和用户信息
      localStorage.setItem("token", token);
      localStorage.setItem("userInfo", JSON.stringify(user));

      commit("SET_TOKEN", token);
      commit("SET_USER_INFO", user);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "登录失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取SM2挑战值
  async getSM2Challenge({ commit, dispatch }, { studentId }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getSM2Challenge(studentId);
      const { challenge } = response.data;

      commit("SET_SM2_CHALLENGE", challenge);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取SM2挑战值失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // SM2证书登录
  async sm2Login({ commit, dispatch }, { studentId, signature }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.sm2Login(studentId, signature);
      const { token, user } = response.data;

      // 保存令牌和用户信息
      localStorage.setItem("token", token);
      localStorage.setItem("userInfo", JSON.stringify(user));

      commit("SET_TOKEN", token);
      commit("SET_USER_INFO", user);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "SM2登录失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 用户注册
  async register({ dispatch }, userData) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.register(userData);
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "注册失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取用户信息
  async getUserInfo({ commit, dispatch, state }) {
    // 如果没有令牌，则不获取用户信息
    if (!state.token) {
      return;
    }

    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getUserInfo();
      const userInfo = response.data;

      // 保存用户信息
      localStorage.setItem("userInfo", JSON.stringify(userInfo));
      commit("SET_USER_INFO", userInfo);

      return userInfo;
    } catch (error) {
      const message = error.response?.data?.message || "获取用户信息失败";

      // 如果是401错误，则清除用户数据
      if (error.response?.status === 401) {
        dispatch("logout");
      }

      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 更新用户信息
  async updateProfile({ commit, dispatch }, userData) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.updateProfile(userData);
      const userInfo = response.data;

      // 保存用户信息
      localStorage.setItem("userInfo", JSON.stringify(userInfo));
      commit("SET_USER_INFO", userInfo);

      return userInfo;
    } catch (error) {
      const message = error.response?.data?.message || "更新用户信息失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 修改密码
  async changePassword({ dispatch }, { oldPassword, newPassword }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.changePassword(oldPassword, newPassword);
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "修改密码失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 更新公钥
  async updatePublicKey({ commit, dispatch }, { publicKey }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.updatePublicKey(publicKey);
      const userInfo = response.data;

      // 保存用户信息
      localStorage.setItem("userInfo", JSON.stringify(userInfo));
      commit("SET_USER_INFO", userInfo);

      return userInfo;
    } catch (error) {
      const message = error.response?.data?.message || "更新公钥失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 移除公钥
  async removePublicKey({ commit, dispatch }) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.removePublicKey();
      const userInfo = response.data;

      // 保存用户信息
      localStorage.setItem("userInfo", JSON.stringify(userInfo));
      commit("SET_USER_INFO", userInfo);

      return userInfo;
    } catch (error) {
      const message = error.response?.data?.message || "移除公钥失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 获取信誉分记录
  async getCreditRecords({ commit, dispatch }, params = {}) {
    try {
      dispatch("setLoading", true, { root: true });

      const response = await api.getCreditRecords(params);
      commit("SET_CREDIT_RECORDS", response.data);

      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || "获取信誉分记录失败";
      dispatch("setError", message, { root: true });
      throw new Error(message);
    } finally {
      dispatch("setLoading", false, { root: true });
    }
  },

  // 退出登录
  async logout({ commit, dispatch }) {
    try {
      // 调用退出登录接口
      await api.logout();
    } catch (error) {
      // 忽略错误
    } finally {
      // 清除本地存储
      localStorage.removeItem("token");
      localStorage.removeItem("userInfo");

      // 清除状态
      commit("CLEAR_USER_DATA");

      // 清除错误
      dispatch("clearError", null, { root: true });
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
