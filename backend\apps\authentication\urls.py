from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

# 创建路由器并注册视图集
router = DefaultRouter()
router.register(r'users', views.UserViewSet)
router.register(r'credit-records', views.CreditRecordViewSet, basename='credit-record')

urlpatterns = [
    # 用户注册
    path('register/', views.RegisterView.as_view(), name='register'),

    # 用户登录
    path('login/', views.LoginView.as_view(), name='login'),

    # 用户登出
    path('logout/', views.LogoutView.as_view(), name='logout'),

    # SM2证书登录
    path('sm2-challenge/', views.SM2ChallengeView.as_view(), name='sm2-challenge'),
    path('sm2-login/', views.SM2LoginView.as_view(), name='sm2-login'),

    # JWT令牌刷新
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # 包含路由器生成的URL
    path('', include(router.urls)),
]
