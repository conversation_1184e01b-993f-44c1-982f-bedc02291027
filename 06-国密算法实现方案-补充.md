# 图书馆自习室管理系统 - 国密算法实现方案补充

## 一、混合加密方案前后端编码转换规范

### 1.1 问题背景

在项目开发过程中，前后端使用不同的国密算法库（前端使用sm-crypto，后端使用gmssl）导致了多种编码和格式不一致问题，主要表现在：

1. **数据编码不一致**：前端使用十六进制字符串，后端使用字节数组
2. **SM2加密模式不一致**：前端默认使用C1C3C2模式，后端默认使用C1C2C3模式
3. **SM4密钥格式不一致**：前端使用十六进制字符串，后端使用字节格式
4. **IV向量处理不一致**：前端IV向量与密文分离，后端需要合并处理
5. **Base64编码处理不一致**：前后端Base64编码/解码方式不同

这些不一致导致了前后端加解密过程中的兼容性问题，特别是在用户注册和登录过程中出现了"Invalid padding bytes"等错误。

### 1.2 统一编码标准

为解决上述问题，制定以下统一编码标准：

#### 1.2.1 基础数据编码规范

| 数据类型 | 编码格式 | 说明 |
|---------|---------|------|
| 字符串 | UTF-8 | 所有文本数据统一使用UTF-8编码 |
| 二进制数据 | Base64 | 网络传输中的二进制数据统一使用Base64编码 |
| 十六进制字符串 | 小写，不带0x前缀 | 如"1a2b3c4d"而非"0x1A2B3C4D" |

#### 1.2.2 密钥格式规范

| 密钥类型 | 格式 | 长度 | 示例 |
|---------|------|------|------|
| SM2公钥 | 带04前缀的十六进制字符串 | 130字符(含04前缀) | "04" + 128字符 |
| SM2私钥 | 十六进制字符串 | 64字符(32字节) | 64个十六进制字符 |
| SM4密钥 | 十六进制字符串 | 32字符(16字节) | 32个十六进制字符 |

### 1.3 SM4加密参数统一

为确保前后端SM4加密/解密兼容，统一以下参数：

| 参数 | 统一值 | 说明 |
|------|-------|------|
| 工作模式 | CBC | 密码分组链接模式，提供更好的安全性 |
| 填充方式 | PKCS#7 | 标准填充方式，确保数据长度符合分组要求 |
| IV长度 | 16字节 | 初始向量固定为16字节(128位) |
| IV生成方式 | 随机生成 | 每次加密使用随机生成的IV |
| IV传输方式 | IV+密文拼接 | IV在前，密文在后，一起Base64编码传输 |

### 1.4 SM2加密模式统一

为解决前后端SM2加密/解密不兼容问题：

| 参数 | 统一值 | 说明 |
|------|-------|------|
| 加密模式 | C1C3C2 | 统一使用C1C3C2模式 |
| 公钥格式 | 非压缩格式(带04前缀) | 确保前后端公钥格式一致 |
| 结果编码 | Base64 | 加密结果统一使用Base64编码传输 |

## 二、混合加密方案实现细节

### 2.1 数据传输格式规范

#### 2.1.1 加密请求数据格式

```json
{
  "encryptedData": "Base64编码的SM4加密数据",
  "encryptedKey": "Base64编码的SM2加密SM4密钥",
  "timestamp": 1638320000000,
  "nonce": "随机字符串"
}
```

#### 2.1.2 SM4加密数据结构

```
[16字节IV][加密后的数据]
```

### 2.2 前端加密实现

```javascript
/**
 * 混合加密数据
 * @param {Object} data - 待加密的数据对象
 * @param {string} serverPublicKey - 服务器SM2公钥
 * @returns {Object} - 加密后的请求数据
 */
function encryptData(data, serverPublicKey) {
  // 1. 生成随机SM4密钥(16字节，32位十六进制字符串)
  const sm4Key = generateRandomHex(32);
  
  // 2. 生成随机IV(16字节，32位十六进制字符串)
  const iv = generateRandomHex(32);
  
  // 3. 将数据转换为JSON字符串
  const jsonData = JSON.stringify(data);
  
  // 4. 使用SM4-CBC模式加密数据
  const encryptedData = sm4.encrypt(jsonData, sm4Key, {
    mode: 'cbc',
    iv: iv,
    padding: 'pkcs#7'
  });
  
  // 5. 拼接IV和密文，并Base64编码
  const ivAndEncryptedData = iv + encryptedData;
  const base64EncryptedData = btoa(hexToBytes(ivAndEncryptedData));
  
  // 6. 使用服务器SM2公钥加密SM4密钥
  const encryptedKey = sm2.doEncrypt(sm4Key, serverPublicKey, 1); // 使用C1C2C3模式
  const base64EncryptedKey = btoa(encryptedKey);
  
  // 7. 构建最终请求数据
  return {
    encryptedData: base64EncryptedData,
    encryptedKey: base64EncryptedKey,
    timestamp: Date.now(),
    nonce: generateNonce()
  };
}

/**
 * 生成指定长度的随机十六进制字符串
 * @param {number} length - 字符串长度
 * @returns {string} - 随机十六进制字符串
 */
function generateRandomHex(length) {
  const bytes = new Uint8Array(length / 2);
  window.crypto.getRandomValues(bytes);
  return Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * 将十六进制字符串转换为字节字符串
 * @param {string} hex - 十六进制字符串
 * @returns {string} - 字节字符串
 */
function hexToBytes(hex) {
  let bytes = '';
  for (let i = 0; i < hex.length; i += 2) {
    bytes += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
  }
  return bytes;
}

/**
 * 生成随机nonce
 * @returns {string} - 随机字符串
 */
function generateNonce() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}
```

### 2.3 后端解密实现

```python
def decrypt_request_data(request_data):
    """
    解密前端加密的请求数据
    
    参数:
        request_data: 包含加密数据的请求字典
    
    返回:
        解密后的原始数据(字典)
    """
    try:
        # 1. 获取加密数据和加密密钥
        encrypted_data_base64 = request_data.get('encryptedData')
        encrypted_key_base64 = request_data.get('encryptedKey')
        
        if not encrypted_data_base64 or not encrypted_key_base64:
            raise ValueError("缺少加密数据或加密密钥")
        
        # 2. Base64解码
        encrypted_data_bytes = base64.b64decode(encrypted_data_base64)
        encrypted_key_bytes = base64.b64decode(encrypted_key_base64)
        
        # 3. 转换为十六进制字符串
        encrypted_data_hex = encrypted_data_bytes.hex()
        encrypted_key_hex = encrypted_key_bytes.hex()
        
        # 4. 提取IV(前32位十六进制字符，即16字节)
        iv_hex = encrypted_data_hex[:32]
        ciphertext_hex = encrypted_data_hex[32:]
        
        # 5. 使用SM2私钥解密SM4密钥
        sm2_crypt = sm2.CryptSM2(
            public_key=None,
            private_key=settings.SM2_PRIVATE_KEY
        )
        sm4_key_hex = sm2_crypt.decrypt(encrypted_key_hex)
        
        # 6. 使用SM4密钥解密数据
        sm4_crypt = SM4()
        sm4_crypt.set_key(bytes.fromhex(sm4_key_hex), SM4.SM4_DECRYPT)
        decrypted_data = sm4_crypt.crypt_cbc(
            bytes.fromhex(iv_hex),
            bytes.fromhex(ciphertext_hex)
        )
        
        # 7. 去除填充并解析JSON
        unpadder = padding.PKCS7(128).unpadder()
        data = unpadder.update(decrypted_data) + unpadder.finalize()
        json_data = json.loads(data.decode('utf-8'))
        
        return json_data
    
    except Exception as e:
        logger.error(f"解密请求数据失败: {str(e)}")
        raise ValueError(f"数据解密失败: {str(e)}")
```

## 三、工具函数与辅助类

### 3.1 前端工具函数

```javascript
/**
 * 前端加密工具类
 */
const CryptoUtils = {
  /**
   * 获取服务器SM2公钥
   * @returns {Promise<string>} 服务器公钥
   */
  async getServerPublicKey() {
    // 从API获取服务器公钥，避免硬编码
    const response = await fetch('/api/v1/crypto/server-public-key');
    const data = await response.json();
    return data.publicKey;
  },
  
  /**
   * 加密请求数据
   * @param {Object} data - 原始数据
   * @returns {Promise<Object>} - 加密后的数据
   */
  async encryptRequest(data) {
    const serverPublicKey = await this.getServerPublicKey();
    return encryptData(data, serverPublicKey);
  },
  
  /**
   * 生成SM2密钥对
   * @returns {Object} - 包含公钥和私钥的对象
   */
  generateSM2KeyPair() {
    return sm2.generateKeyPairHex();
  },
  
  /**
   * 安全存储SM2私钥
   * @param {string} privateKey - SM2私钥
   * @param {string} password - 保护密码
   */
  storePrivateKey(privateKey, password) {
    // 使用密码加密私钥后存储
    const encryptedPrivateKey = sm4.encrypt(privateKey, this.deriveKeyFromPassword(password));
    localStorage.setItem('sm2_private_key', encryptedPrivateKey);
  },
  
  /**
   * 从密码派生密钥
   * @param {string} password - 用户密码
   * @returns {string} - 派生的密钥
   */
  deriveKeyFromPassword(password) {
    // 简化实现，实际应使用PBKDF2等密钥派生函数
    return sm3.hash(password).substring(0, 32);
  }
};
```

### 3.2 后端工具类

```python
class CryptoUtils:
    """加密工具类"""
    
    @staticmethod
    def generate_sm2_keypair():
        """生成SM2密钥对"""
        private_key = func.random_hex(32)
        sm2_crypt = sm2.CryptSM2(private_key=private_key, public_key=None)
        public_key = sm2_crypt.public_key.hex()
        return {
            'private_key': private_key,
            'public_key': public_key
        }
    
    @staticmethod
    def generate_sm4_key():
        """生成随机SM4密钥"""
        return os.urandom(16).hex()
    
    @staticmethod
    def sm4_encrypt(data, key, iv=None):
        """
        SM4加密
        
        参数:
            data: 待加密数据(字符串或字节)
            key: SM4密钥(十六进制字符串)
            iv: 初始向量(十六进制字符串，可选)
            
        返回:
            加密结果(十六进制字符串，包含IV+密文)
        """
        # 确保数据为字节
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        # 确保密钥为字节
        if isinstance(key, str):
            key = bytes.fromhex(key)
        
        # 生成或转换IV
        if iv is None:
            iv = os.urandom(16)
        elif isinstance(iv, str):
            iv = bytes.fromhex(iv)
        
        # 创建SM4加密器
        sm4_crypt = SM4()
        sm4_crypt.set_key(key, SM4.SM4_ENCRYPT)
        
        # 填充数据
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data) + padder.finalize()
        
        # 加密数据
        ciphertext = sm4_crypt.crypt_cbc(iv, padded_data)
        
        # 返回IV+密文的十六进制字符串
        return iv.hex() + ciphertext.hex()
    
    @staticmethod
    def sm4_decrypt(encrypted_data, key):
        """
        SM4解密
        
        参数:
            encrypted_data: 加密数据(十六进制字符串，包含IV+密文)
            key: SM4密钥(十六进制字符串)
            
        返回:
            解密结果(字节)
        """
        # 确保密钥为字节
        if isinstance(key, str):
            key = bytes.fromhex(key)
        
        # 确保加密数据为十六进制字符串
        if isinstance(encrypted_data, bytes):
            encrypted_data = encrypted_data.hex()
        
        # 提取IV和密文
        iv = bytes.fromhex(encrypted_data[:32])
        ciphertext = bytes.fromhex(encrypted_data[32:])
        
        # 创建SM4解密器
        sm4_crypt = SM4()
        sm4_crypt.set_key(key, SM4.SM4_DECRYPT)
        
        # 解密数据
        padded_data = sm4_crypt.crypt_cbc(iv, ciphertext)
        
        # 去除填充
        unpadder = padding.PKCS7(128).unpadder()
        return unpadder.update(padded_data) + unpadder.finalize()
```

## 四、常见问题与解决方案

### 4.1 前后端加解密兼容性问题

| 问题 | 解决方案 |
|------|---------|
| SM4密钥格式不一致 | 统一使用十六进制字符串表示，前后端转换时注意格式 |
| SM2加密模式不一致 | 前端调用sm2.doEncrypt时指定mode=1(C1C2C3模式) |
| IV向量处理不一致 | 统一IV与密文拼接方式，前16字节为IV |
| Base64编码问题 | 确保二进制数据先转为字节字符串再Base64编码 |
| 填充错误 | 确保使用相同的PKCS#7填充方式，检查密钥是否正确 |

### 4.2 调试与测试方法

1. **加密过程可视化**：
   - 在前端记录每一步加密过程的中间结果
   - 使用开发者工具观察网络请求中的加密数据

2. **解密过程日志**：
   - 在后端记录详细的解密步骤日志
   - 捕获并记录具体的解密错误信息

3. **端到端测试**：
   - 创建专门的加解密测试API
   - 使用已知数据和密钥进行加解密测试

4. **密钥验证**：
   - 验证前端获取的服务器公钥是否正确
   - 确认SM4密钥能被正确加密和解密
