/**
 * 国密算法工具类
 * 提供SM2/SM3/SM4加解密功能
 */
import { sm2, sm3, sm4 } from "sm-crypto";

/**
 * SM3哈希工具类
 */
export class SM3Hasher {
  /**
   * 计算SM3哈希值
   * @param {string} data - 待哈希数据
   * @returns {string} - 哈希值(十六进制字符串)
   */
  static hash(data) {
    return sm3(data);
  }

  /**
   * 使用盐值和多轮迭代计算SM3哈希
   * @param {string} data - 待哈希数据
   * @param {string} salt - 盐值(十六进制字符串)，如果为null则随机生成
   * @param {number} iterations - 迭代次数
   * @returns {Object} - 包含哈希值、盐值和迭代次数的对象
   */
  static hashWithSalt(data, salt = null, iterations = 10000) {
    // 如果没有提供盐值，生成随机盐值
    if (!salt) {
      salt = this.generateRandomHex(32);
    }

    // 初始哈希值为数据和盐值的组合
    let value = data + salt;

    // 多轮迭代哈希
    for (let i = 0; i < iterations; i++) {
      value = sm3(value);
    }

    return {
      hash: value,
      salt: salt,
      iterations: iterations,
    };
  }

  /**
   * 验证数据的哈希值是否匹配
   * @param {string} data - 待验证数据
   * @param {string} hashValue - 哈希值(十六进制字符串)
   * @param {string} salt - 盐值(十六进制字符串)
   * @param {number} iterations - 迭代次数
   * @returns {boolean} - 是否匹配
   */
  static verify(data, hashValue, salt, iterations = 10000) {
    const result = this.hashWithSalt(data, salt, iterations);
    return result.hash === hashValue;
  }

  /**
   * 生成随机十六进制字符串
   * @param {number} length - 字节长度
   * @returns {string} - 十六进制字符串
   */
  static generateRandomHex(length) {
    const bytes = new Uint8Array(length);
    window.crypto.getRandomValues(bytes);
    return Array.from(bytes)
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("");
  }
}

/**
 * SM4加解密工具类
 */
export class SM4Crypto {
  /**
   * 生成随机SM4密钥
   * @returns {string} - 密钥(十六进制字符串)
   */
  static generateKey() {
    return SM3Hasher.generateRandomHex(16);
  }

  /**
   * SM4加密
   * @param {string} key - SM4密钥(十六进制字符串)
   * @param {string} data - 待加密数据
   * @param {string} iv - 初始向量(十六进制字符串)，如果为null则随机生成
   * @param {string} mode - 加密模式，支持'cbc'和'ecb'
   * @returns {Object} - 包含密文和IV的对象
   */
  static encrypt(key, data, iv = null, mode = "cbc") {
    try {
      // 如果没有提供IV，生成随机IV
      if (!iv && mode.toLowerCase() === "cbc") {
        iv = SM3Hasher.generateRandomHex(16);
      }

      let ciphertext;
      if (mode.toLowerCase() === "cbc") {
        // CBC模式加密
        ciphertext = sm4.encrypt(data, key, {
          mode: "cbc",
          iv: iv,
        });
        return {
          ciphertext: ciphertext,
          iv: iv,
        };
      } else if (mode.toLowerCase() === "ecb") {
        // ECB模式加密
        ciphertext = sm4.encrypt(data, key);
        return {
          ciphertext: ciphertext,
        };
      } else {
        throw new Error(`不支持的加密模式: ${mode}`);
      }
    } catch (error) {
      console.error("SM4加密失败:", error);
      throw new Error("SM4加密操作失败");
    }
  }

  /**
   * SM4解密
   * @param {string} key - SM4密钥(十六进制字符串)
   * @param {string} ciphertext - 密文(十六进制字符串)
   * @param {string} iv - 初始向量(十六进制字符串)，CBC模式必须提供
   * @param {string} mode - 解密模式，支持'cbc'和'ecb'
   * @returns {string} - 解密结果
   */
  static decrypt(key, ciphertext, iv = null, mode = "cbc") {
    try {
      if (mode.toLowerCase() === "cbc") {
        if (!iv) {
          throw new Error("CBC模式解密必须提供IV");
        }
        // CBC模式解密
        return sm4.decrypt(ciphertext, key, {
          mode: "cbc",
          iv: iv,
        });
      } else if (mode.toLowerCase() === "ecb") {
        // ECB模式解密
        return sm4.decrypt(ciphertext, key);
      } else {
        throw new Error(`不支持的解密模式: ${mode}`);
      }
    } catch (error) {
      console.error("SM4解密失败:", error);
      throw new Error("SM4解密操作失败");
    }
  }
}

/**
 * SM2加解密和签名工具类
 */
export class SM2Crypto {
  /**
   * 生成SM2密钥对
   * @returns {Object} - 包含公钥和私钥的对象
   */
  static generateKeyPair() {
    try {
      return sm2.generateKeyPairHex();
    } catch (error) {
      console.error("生成SM2密钥对失败:", error);
      throw new Error("生成SM2密钥对失败");
    }
  }

  /**
   * SM2加密
   * @param {string} publicKey - SM2公钥(十六进制字符串)
   * @param {string} data - 待加密数据
   * @returns {string} - 加密结果(十六进制字符串)
   */
  static encrypt(publicKey, data) {
    try {
      return sm2.doEncrypt(data, publicKey, 1); // 使用C1C3C2模式
    } catch (error) {
      console.error("SM2加密失败:", error);
      throw new Error("SM2加密操作失败");
    }
  }

  /**
   * SM2解密
   * @param {string} privateKey - SM2私钥(十六进制字符串)
   * @param {string} encryptedData - 加密数据(十六进制字符串)
   * @returns {string} - 解密结果
   */
  static decrypt(privateKey, encryptedData) {
    try {
      return sm2.doDecrypt(encryptedData, privateKey, 1); // 使用C1C3C2模式
    } catch (error) {
      console.error("SM2解密失败:", error);
      throw new Error("SM2解密操作失败");
    }
  }

  /**
   * SM2签名
   * @param {string} privateKey - SM2私钥(十六进制字符串)
   * @param {string} data - 待签名数据
   * @returns {string} - 签名(十六进制字符串)
   */
  static sign(privateKey, data) {
    try {
      return sm2.doSignature(data, privateKey, {
        der: true, // 使用DER编码
        hash: true, // 对消息做SM3哈希
      });
    } catch (error) {
      console.error("SM2签名失败:", error);
      throw new Error("SM2签名操作失败");
    }
  }

  /**
   * SM2验签
   * @param {string} publicKey - SM2公钥(十六进制字符串)
   * @param {string} data - 原始数据
   * @param {string} signature - 签名(十六进制字符串)
   * @returns {boolean} - 验证结果
   */
  static verify(publicKey, data, signature) {
    try {
      return sm2.doVerifySignature(data, signature, publicKey, {
        der: true, // 使用DER编码
        hash: true, // 对消息做SM3哈希
      });
    } catch (error) {
      console.error("SM2验签失败:", error);
      return false;
    }
  }

  /**
   * 从私钥获取公钥
   * @param {string} privateKey - SM2私钥(十六进制字符串)
   * @returns {string} - 公钥(十六进制字符串)
   */
  static getPublicKeyFromPrivate(privateKey) {
    try {
      return sm2.getPublicKeyFromPrivateKey(privateKey);
    } catch (error) {
      console.error("从私钥获取公钥失败:", error);
      throw new Error("获取公钥失败");
    }
  }
}
