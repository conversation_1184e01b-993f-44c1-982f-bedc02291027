"""
国密算法API视图
提供密钥获取和加解密服务
"""
import json
import logging
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.conf import settings
from utils.crypto_utils import CryptoUtils
from utils.key_manager import KeyManager

logger = logging.getLogger(__name__)

class ServerPublicKeyView(APIView):
    """服务器公钥获取视图"""
    permission_classes = [AllowAny]
    
    def get(self, request):
        """获取服务器SM2公钥"""
        try:
            public_key = CryptoUtils.get_server_public_key()
            return Response({
                'publicKey': public_key,
                'algorithm': 'SM2',
                'format': 'hex'
            })
        except Exception as e:
            logger.error(f"获取服务器公钥失败: {str(e)}")
            return Response({
                'error': '获取服务器公钥失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EncryptionTestView(APIView):
    """加解密测试视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """测试混合加密方案"""
        try:
            # 获取加密包
            encrypted_package = request.data
            
            # 解密数据
            decrypted_data = CryptoUtils.decrypt_from_transmission(encrypted_package)
            
            # 处理解密后的数据
            if isinstance(decrypted_data, str):
                try:
                    decrypted_data = json.loads(decrypted_data)
                except json.JSONDecodeError:
                    pass
            
            # 准备响应数据
            response_data = {
                'message': '加解密测试成功',
                'received_data': decrypted_data
            }
            
            # 加密响应数据
            encrypted_response = CryptoUtils.encrypt_for_transmission(response_data)
            
            # 返回加密响应
            return Response(encrypted_response, headers={'X-Encrypted': 'true'})
        except Exception as e:
            logger.error(f"加解密测试失败: {str(e)}")
            return Response({
                'error': '加解密测试失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SM2KeyPairGeneratorView(APIView):
    """SM2密钥对生成视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """生成SM2密钥对"""
        try:
            key_pair = CryptoUtils.generate_sm2_keypair()
            return Response({
                'publicKey': key_pair['public_key'],
                'privateKey': key_pair['private_key'],
                'algorithm': 'SM2',
                'format': 'hex'
            })
        except Exception as e:
            logger.error(f"生成SM2密钥对失败: {str(e)}")
            return Response({
                'error': '生成SM2密钥对失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SM4KeyGeneratorView(APIView):
    """SM4密钥生成视图"""
    permission_classes = [AllowAny]
    
    def post(self, request):
        """生成SM4密钥"""
        try:
            key = CryptoUtils.generate_sm4_key()
            return Response({
                'key': key,
                'algorithm': 'SM4',
                'format': 'hex'
            })
        except Exception as e:
            logger.error(f"生成SM4密钥失败: {str(e)}")
            return Response({
                'error': '生成SM4密钥失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
