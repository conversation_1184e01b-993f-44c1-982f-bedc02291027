from rest_framework import serializers
from .models import Room, Seat, Reservation, SeatOperation, BlacklistRecord
from apps.authentication.models import User
from utils.crypto import SM3Hasher
import secrets
import datetime
from django.utils import timezone

class RoomSerializer(serializers.ModelSerializer):
    """自习室序列化器"""
    available_seats = serializers.SerializerMethodField()

    class Meta:
        model = Room
        fields = ['id', 'name', 'location', 'floor', 'capacity', 'open_time',
                  'close_time', 'status', 'description', 'available_seats']

    def get_available_seats(self, obj):
        """获取可用座位数量"""
        return obj.seat_set.filter(status='available').count()


class SeatSerializer(serializers.ModelSerializer):
    """座位序列化器"""
    room_name = serializers.CharField(source='room.name', read_only=True)

    class Meta:
        model = Seat
        fields = ['id', 'room', 'room_name', 'seat_number', 'row', 'column',
                  'status', 'is_power_outlet', 'is_window_seat']


class SeatDetailSerializer(serializers.ModelSerializer):
    """座位详情序列化器"""
    room = RoomSerializer(read_only=True)
    current_reservation = serializers.SerializerMethodField()

    class Meta:
        model = Seat
        fields = ['id', 'room', 'seat_number', 'row', 'column', 'status',
                  'is_power_outlet', 'is_window_seat', 'current_reservation']

    def get_current_reservation(self, obj):
        """获取当前座位的预约信息"""
        now = timezone.now()
        reservation = Reservation.objects.filter(
            seat=obj,
            start_time__lte=now,
            end_time__gte=now,
            status__in=['pending', 'checked_in']
        ).first()

        if reservation:
            return {
                'id': reservation.id,
                'status': reservation.status,
                'start_time': reservation.start_time,
                'end_time': reservation.end_time,
                'user_id': reservation.user_id
            }
        return None


class ReservationSerializer(serializers.ModelSerializer):
    """预约序列化器"""
    seat_number = serializers.CharField(source='seat.seat_number', read_only=True)
    room_name = serializers.CharField(source='seat.room.name', read_only=True)

    class Meta:
        model = Reservation
        fields = ['id', 'user', 'seat', 'seat_number', 'room_name', 'reservation_code',
                  'start_time', 'end_time', 'status', 'check_in_time', 'check_out_time',
                  'created_at']
        read_only_fields = ['id', 'user', 'reservation_code', 'check_in_time',
                           'check_out_time', 'created_at']

    def validate(self, attrs):
        seat = attrs.get('seat')
        start_time = attrs.get('start_time')
        end_time = attrs.get('end_time')

        # 验证开始时间和结束时间
        if start_time >= end_time:
            raise serializers.ValidationError({'end_time': '结束时间必须晚于开始时间'})

        # 验证预约时间是否在自习室开放时间内
        room = seat.room
        start_time_only = start_time.time()
        end_time_only = end_time.time()

        if start_time_only < room.open_time or end_time_only > room.close_time:
            raise serializers.ValidationError({'time': '预约时间必须在自习室开放时间内'})

        # 验证座位是否可用
        if seat.status != 'available':
            raise serializers.ValidationError({'seat': '该座位当前不可用'})

        # 验证是否与其他预约冲突
        conflicting_reservations = Reservation.objects.filter(
            seat=seat,
            status__in=['pending', 'checked_in'],
            start_time__lt=end_time,
            end_time__gt=start_time
        )

        if conflicting_reservations.exists():
            raise serializers.ValidationError({'seat': '该座位在所选时间段内已被预约'})

        # 验证用户是否有其他活跃预约
        user = self.context['request'].user
        active_reservations = Reservation.objects.filter(
            user=user,
            status__in=['pending', 'checked_in'],
            end_time__gt=timezone.now()
        )

        if active_reservations.exists() and not self.instance:
            raise serializers.ValidationError({'user': '您已有活跃的预约，请先完成或取消当前预约'})

        # 验证用户信誉分
        if user.credit_score < 60:
            raise serializers.ValidationError({'user': '您的信誉分不足，无法预约座位'})

        return attrs

    def create(self, validated_data):
        user = self.context['request'].user

        # 生成预约码（使用SM3哈希）
        reservation_data = f"{user.id}_{validated_data['seat'].id}_{validated_data['start_time']}_{secrets.token_hex(8)}"
        reservation_code = SM3Hasher.hash(reservation_data)

        # 创建预约记录
        reservation = Reservation.objects.create(
            user=user,
            seat=validated_data['seat'],
            reservation_code=reservation_code,
            start_time=validated_data['start_time'],
            end_time=validated_data['end_time'],
            status='pending'
        )

        # 更新座位状态
        seat = validated_data['seat']

        # 记录座位操作
        SeatOperation.objects.create(
            seat=seat,
            operation_type='reserve',
            user=user,
            reservation=reservation,
            status_before=seat.status,
            status_after='reserved',
            ip_address=self.context['request'].META.get('REMOTE_ADDR', ''),
            hash_value=SM3Hasher.hash(f"{seat.id}_{user.id}_reserve_{timezone.now().isoformat()}"),
            prev_hash=SeatOperation.objects.filter(seat=seat).order_by('-created_at').first().hash_value if SeatOperation.objects.filter(seat=seat).exists() else None
        )

        seat.status = 'reserved'
        seat.save()

        return reservation


class ReservationCheckInSerializer(serializers.Serializer):
    """预约签到序列化器"""
    reservation_code = serializers.CharField(max_length=64, required=True)
    signature = serializers.CharField(required=False)

    def validate(self, attrs):
        reservation_code = attrs.get('reservation_code')

        try:
            reservation = Reservation.objects.get(reservation_code=reservation_code)
        except Reservation.DoesNotExist:
            raise serializers.ValidationError({'reservation_code': '无效的预约码'})

        # 验证预约状态
        if reservation.status != 'pending':
            raise serializers.ValidationError({'reservation': f'预约状态不允许签到: {reservation.status}'})

        # 验证预约时间
        now = timezone.now()
        if now < reservation.start_time - datetime.timedelta(minutes=15):
            raise serializers.ValidationError({'reservation': '签到时间未到，请在预约开始前15分钟内签到'})

        if now > reservation.start_time + datetime.timedelta(minutes=15):
            raise serializers.ValidationError({'reservation': '已超过签到时间，预约已失效'})

        attrs['reservation'] = reservation
        return attrs


class ReservationCheckOutSerializer(serializers.Serializer):
    """预约签退序列化器"""
    reservation_id = serializers.IntegerField(required=True)
    signature = serializers.CharField(required=False)

    def validate(self, attrs):
        reservation_id = attrs.get('reservation_id')
        user = self.context['request'].user

        try:
            reservation = Reservation.objects.get(id=reservation_id, user=user)
        except Reservation.DoesNotExist:
            raise serializers.ValidationError({'reservation_id': '无效的预约ID'})

        # 验证预约状态
        if reservation.status != 'checked_in':
            raise serializers.ValidationError({'reservation': f'预约状态不允许签退: {reservation.status}'})

        attrs['reservation'] = reservation
        return attrs


class SeatOperationSerializer(serializers.ModelSerializer):
    """座位操作记录序列化器"""
    seat_number = serializers.CharField(source='seat.seat_number', read_only=True)
    room_name = serializers.CharField(source='seat.room.name', read_only=True)

    class Meta:
        model = SeatOperation
        fields = ['id', 'seat', 'seat_number', 'room_name', 'operation_type',
                  'user', 'reservation', 'status_before', 'status_after',
                  'ip_address', 'created_at']
        read_only_fields = fields
