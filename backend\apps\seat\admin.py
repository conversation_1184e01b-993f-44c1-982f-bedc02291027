from django.contrib import admin
from .models import Room, Seat, Reservation, SeatOperation, BlacklistRecord

class RoomAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'location', 'floor', 'capacity', 'status', 'open_time', 'close_time')
    list_filter = ('status', 'floor')
    search_fields = ('name', 'location')
    ordering = ('floor', 'name')

class SeatAdmin(admin.ModelAdmin):
    list_display = ('id', 'room', 'seat_number', 'row', 'column', 'status', 'is_power_outlet', 'is_window_seat')
    list_filter = ('status', 'room', 'is_power_outlet', 'is_window_seat')
    search_fields = ('seat_number', 'room__name')
    ordering = ('room', 'row', 'column')

class ReservationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'seat', 'status', 'start_time', 'end_time', 'check_in_time', 'created_at')
    list_filter = ('status',)
    search_fields = ('user__student_id_hash', 'seat__seat_number', 'reservation_code')
    ordering = ('-created_at',)
    date_hierarchy = 'start_time'

class SeatOperationAdmin(admin.ModelAdmin):
    list_display = ('id', 'seat', 'operation_type', 'user', 'status_before', 'status_after', 'created_at')
    list_filter = ('operation_type',)
    search_fields = ('seat__seat_number', 'user__student_id_hash')
    ordering = ('-created_at',)

class BlacklistRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'reason', 'status', 'start_time', 'end_time', 'operator_type', 'created_at')
    list_filter = ('status', 'operator_type')
    search_fields = ('user__student_id_hash', 'reason')
    ordering = ('-created_at',)
    date_hierarchy = 'start_time'

admin.site.register(Room, RoomAdmin)
admin.site.register(Seat, SeatAdmin)
admin.site.register(Reservation, ReservationAdmin)
admin.site.register(SeatOperation, SeatOperationAdmin)
admin.site.register(BlacklistRecord, BlacklistRecordAdmin)
