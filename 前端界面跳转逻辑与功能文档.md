# 前端界面跳转逻辑与功能文档

## 1. 整体架构

图书馆自习室管理系统前端基于Vue 3 + Element Plus开发，采用Vuex进行状态管理，Vue Router进行路由管理。系统使用sm-crypto库实现前端国密算法加密功能。

### 1.1 技术栈

- **框架**：Vue 3
- **UI组件库**：Element Plus
- **状态管理**：Vuex 4
- **路由管理**：Vue Router 4
- **HTTP请求**：Axios
- **国密算法**：sm-crypto

### 1.2 项目结构

前端项目主要包含以下目录结构：

- `src/components`：公共组件
- `src/views`：页面视图
- `src/router`：路由配置
- `src/store`：状态管理
- `src/api`：API接口
- `src/utils`：工具函数
- `src/assets`：静态资源

## 2. 路由与页面跳转

### 2.1 主要路由配置

系统路由配置在`src/router/index.js`中定义，主要包含以下路由：

| 路径 | 组件 | 说明 | 权限要求 |
|------|------|------|----------|
| `/` | Layout | 主布局（重定向到dashboard） | - |
| `/dashboard` | Dashboard.vue | 系统首页 | 需要登录 |
| `/login` | Login.vue | 登录页面 | - |
| `/register` | Register.vue | 注册页面 | - |
| `/user/profile` | UserProfile.vue | 个人信息 | 需要登录 |
| `/user/reservations` | MyReservations.vue | 我的预约 | 需要登录 |
| `/user/records` | OperationRecords.vue | 操作记录 | 需要登录 |
| `/user/credit` | CreditRecords.vue | 信誉分记录 | 需要登录 |
| `/seat/rooms` | RoomList.vue | 自习室列表 | 需要登录 |
| `/seat/map` | SeatMap.vue | 座位地图 | 需要登录 |
| `/seat/reservation` | SeatReservation.vue | 座位预约 | 需要登录 |
| `/seat/reservation/:id` | ReservationDetail.vue | 预约详情 | 需要登录 |
| `/help` | Help.vue | 帮助中心 | - |
| `/:pathMatch(.*)*` | NotFound.vue | 404页面 | - |

### 2.2 路由守卫

系统使用全局前置守卫进行权限控制：

```javascript
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - 图书馆自习室管理系统`
    : "图书馆自习室管理系统";

  // 检查是否需要登录
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    // 检查用户是否已登录
    const isLoggedIn = localStorage.getItem("token") !== null;

    if (!isLoggedIn) {
      // 未登录，重定向到登录页
      next({
        path: "/login",
        query: { redirect: to.fullPath },
      });
    } else {
      // 已登录，允许访问
      next();
    }
  } else {
    // 不需要登录，允许访问
    next();
  }
});
```

## 3. 主要页面功能

### 3.1 登录页面 (Login.vue)

**路径**：`/login`

**功能**：
- 提供两种登录方式：密码登录和证书登录
- 密码登录：使用学号和密码（密码经SM3哈希加密后传输）
- 证书登录：使用学号和私钥（基于SM2算法的挑战-响应认证）
- 登录成功后跳转到首页或指定的重定向页面

**跳转逻辑**：
- 登录成功 → 首页(`/dashboard`)
- 点击注册链接 → 注册页面(`/register`)

### 3.2 首页 (Dashboard.vue)

**路径**：`/dashboard`

**功能**：
- 显示系统概览信息：当前可用座位数、我的预约数、我的信誉分
- 提供快捷操作按钮：预约座位、查看我的预约、查看座位地图

**跳转逻辑**：
- 点击"预约座位"按钮 → 座位预约页面(`/seat/reservation`)
- 点击"查看我的预约"按钮 → 我的预约页面(`/user/reservations`)
- 点击"查看座位地图"按钮 → 座位地图页面(`/seat/map`)

### 3.3 自习室列表 (RoomList.vue)

**路径**：`/seat/rooms`

**功能**：
- 显示所有可用自习室列表
- 支持按名称搜索、按楼层/容量/可用座位数过滤和排序
- 显示每个自习室的基本信息：名称、位置、楼层、容量、可用座位数等

**跳转逻辑**：
- 点击自习室卡片 → 座位地图页面(`/seat/map?roomId=xxx`)

### 3.4 座位地图 (SeatMap.vue)

**路径**：`/seat/map?roomId=xxx`

**功能**：
- 显示指定自习室的座位布局图
- 支持按日期查看座位状态
- 支持过滤：只看有电源的座位、只看靠窗座位、只看可用座位
- 点击座位显示详细信息
- 提供座位预约功能

**跳转逻辑**：
- 点击"返回"按钮 → 返回上一页
- 点击"预约"按钮 → 座位预约页面(`/seat/reservation?seatId=xxx&date=xxx`)

### 3.5 座位预约 (SeatReservation.vue)

**路径**：`/seat/reservation?seatId=xxx&date=xxx`

**功能**：
- 分步骤引导用户完成预约：选择座位 → 选择时间 → 确认预约 → 预约成功
- 显示座位和自习室详细信息
- 显示可用时间段列表
- 提交预约请求并显示预约结果

**跳转逻辑**：
- 点击"返回"按钮 → 返回上一页
- 预约成功后可查看预约详情或返回首页

### 3.6 我的预约 (MyReservations.vue)

**路径**：`/user/reservations`

**功能**：
- 显示用户的所有预约记录，包括待签到、已签到、已完成、已取消等状态
- 支持取消预约操作
- 支持查看预约详情

**跳转逻辑**：
- 点击"查看详情"按钮 → 预约详情页面(`/seat/reservation/{id}?mode=view`)

### 3.7 个人信息 (UserProfile.vue)

**路径**：`/user/profile`

**功能**：
- 显示用户基本信息：学号、邮箱、手机号等
- 显示用户公钥和私钥信息
- 支持修改密码和个人信息
- 支持下载私钥和公钥

## 4. 组件交互

### 4.1 布局组件

系统使用以下布局组件构建整体界面：

- **Layout.vue**：主布局组件，包含Header、Sidebar和Footer
- **Header.vue**：顶部导航栏，包含logo和用户菜单
- **Sidebar.vue**：侧边栏导航，包含主要功能菜单
- **Footer.vue**：页脚组件

### 4.2 Header组件交互

Header组件提供用户下拉菜单，包含以下选项：

- 个人中心 → `/user/profile`
- 我的预约 → `/user/reservations`
- 操作记录 → `/user/records`
- 退出登录 → 清除登录状态并跳转到登录页面

### 4.3 Sidebar组件交互

Sidebar组件提供主要功能导航：

- 首页 → `/dashboard`
- 座位管理
  - 自习室列表 → `/seat/rooms`
  - 座位地图 → `/seat/map`
  - 预约座位 → `/seat/reservation`
- 个人中心
  - 个人信息 → `/user/profile`
  - 我的预约 → `/user/reservations`
  - 操作记录 → `/user/records`
  - 信誉分记录 → `/user/credit`
- 帮助中心 → `/help`

## 5. 状态管理

系统使用Vuex进行状态管理，主要包含以下模块：

### 5.1 用户模块 (user)

管理用户相关状态：

- 登录状态
- 用户信息
- 认证令牌
- 信誉分记录

### 5.2 座位模块 (seat)

管理座位相关状态：

- 自习室列表
- 座位信息
- 时间段信息
- 预约记录

## 6. 国密算法应用

系统使用sm-crypto库实现前端国密算法功能：

- **SM2**：用于证书登录的非对称加密和数字签名
- **SM3**：用于密码哈希和数据完整性校验
- **SM4**：用于敏感数据的对称加密
