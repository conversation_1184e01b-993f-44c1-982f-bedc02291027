from django.contrib import admin
from django.contrib.auth.forms import ReadOnlyPasswordHashField
from django import forms
from .models import Admin, AdminSession, SystemConfig, AdminOperationLog
from utils.crypto import SM3Hasher

class AdminCreationForm(forms.ModelForm):
    """管理员创建表单，包含密码字段"""
    password = forms.CharField(label='密码', widget=forms.PasswordInput)

    class Meta:
        model = Admin
        fields = ('username', 'role', 'status')

    def save(self, commit=True):
        # 保存密码的哈希值
        admin = super().save(commit=False)
        password = self.cleaned_data.get('password')
        if password:
            # 使用set_password方法设置密码
            admin.set_password(password)
        if commit:
            admin.save()
        return admin

class AdminChangeForm(forms.ModelForm):
    """管理员修改表单，密码字段为只读"""
    password = ReadOnlyPasswordHashField(
        label='密码',
        help_text='密码以加密形式存储，无法查看原始密码。'
                  '可以通过<a href="../password/">这个表单</a>修改密码。'
    )

    class Meta:
        model = Admin
        fields = ('username', 'password', 'role', 'status')

class AdminPasswordChangeForm(forms.Form):
    """管理员密码修改表单"""
    password = forms.CharField(label='新密码', widget=forms.PasswordInput)
    password_confirm = forms.CharField(label='确认密码', widget=forms.PasswordInput)

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        password_confirm = cleaned_data.get('password_confirm')

        if password and password_confirm and password != password_confirm:
            raise forms.ValidationError('两次输入的密码不一致')

        return cleaned_data

class AdminModelAdmin(admin.ModelAdmin):
    form = AdminChangeForm
    add_form = AdminCreationForm
    list_display = ('id', 'username', 'role', 'status', 'last_login', 'created_at')
    list_filter = ('role', 'status')
    search_fields = ('username',)
    ordering = ('username',)

    def get_form(self, request, obj=None, **kwargs):
        """
        使用不同的表单用于添加和修改
        """
        if obj is None:
            return self.add_form
        return super().get_form(request, obj, **kwargs)

    def save_model(self, request, obj, form, change):
        """
        保存模型时处理密码加密和角色设置
        """
        if not change:  # 创建新管理员时
            # 密码已在表单的save方法中处理
            # 确保auth_key字段为空，避免密码被错误存储
            obj.auth_key = None

            # 设置is_superuser属性
            if obj.role == 'super_admin':
                obj.is_superuser = True

        super().save_model(request, obj, form, change)

    def get_urls(self):
        from django.urls import path
        from django.contrib.admin.views.decorators import staff_member_required

        def wrap(view):
            def wrapper(*args, **kwargs):
                return staff_member_required(view)(*args, **kwargs)
            wrapper.model_admin = self
            return wrapper

        info = self.model._meta.app_label, self.model._meta.model_name

        urlpatterns = super().get_urls()
        my_urls = [
            path(
                '<path:object_id>/password/',
                wrap(self.admin_password_change),
                name='%s_%s_password_change' % info
            ),
        ]
        return my_urls + urlpatterns

    def admin_password_change(self, request, object_id, form_url=''):
        """
        管理员密码修改视图

        参数:
            request: 请求对象
            object_id: 管理员ID
            form_url: 表单URL，Django admin框架传入的参数，可能未使用但需要保留
        """
        from django.contrib import messages
        from django.shortcuts import get_object_or_404, redirect, render
        from django.urls import reverse

        admin_obj = get_object_or_404(self.model, pk=object_id)

        if request.method == 'POST':
            form = AdminPasswordChangeForm(request.POST)
            if form.is_valid():
                password = form.cleaned_data['password']
                # 使用set_password方法设置密码
                admin_obj.set_password(password)
                admin_obj.save()

                # 记录操作日志
                AdminOperationLog.objects.create(
                    admin=request.user,
                    operation='change_password',
                    description=f'修改管理员密码: {admin_obj.username}',
                    ip_address=request.META.get('REMOTE_ADDR', ''),
                    target_type='admin',
                    target_id=admin_obj.id,
                    status='success'
                )

                messages.success(request, '密码已成功修改')
                return redirect(reverse('admin:%s_%s_change' % (
                    self.model._meta.app_label,
                    self.model._meta.model_name
                ), args=[admin_obj.pk]))
        else:
            form = AdminPasswordChangeForm()

        context = {
            'title': '修改密码',
            'form': form,
            'opts': self.model._meta,
            'original': admin_obj,
        }
        return render(request, 'admin/auth/user/change_password.html', context)

class AdminSessionAdmin(admin.ModelAdmin):
    list_display = ('id', 'admin', 'ip_address', 'is_active', 'created_at', 'expires_at')
    list_filter = ('is_active',)
    search_fields = ('admin__username', 'ip_address')
    ordering = ('-created_at',)

class SystemConfigAdmin(admin.ModelAdmin):
    list_display = ('id', 'key', 'description', 'is_encrypted', 'updated_at', 'updated_by')
    list_filter = ('is_encrypted',)
    search_fields = ('key', 'description')
    ordering = ('key',)

class AdminOperationLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'admin', 'operation', 'status', 'ip_address', 'target_type', 'target_id', 'created_at')
    list_filter = ('operation', 'status')
    search_fields = ('admin__username', 'description', 'operation')
    ordering = ('-created_at',)

admin.site.register(Admin, AdminModelAdmin)
admin.site.register(AdminSession, AdminSessionAdmin)
admin.site.register(SystemConfig, SystemConfigAdmin)
admin.site.register(AdminOperationLog, AdminOperationLogAdmin)
