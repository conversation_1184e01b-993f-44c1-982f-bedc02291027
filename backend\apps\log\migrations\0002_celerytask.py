# Generated by Django 4.2.7 on 2025-05-23 03:28

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('log', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CeleryTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.CharField(max_length=255, unique=True, verbose_name='任务ID')),
                ('task_name', models.CharField(max_length=255, verbose_name='任务名称')),
                ('task_args', models.TextField(blank=True, null=True, verbose_name='任务参数')),
                ('task_kwargs', models.TextField(blank=True, null=True, verbose_name='任务关键字参数')),
                ('status', models.Char<PERSON>ield(max_length=50, verbose_name='任务状态')),
                ('result', models.TextField(blank=True, null=True, verbose_name='任务结果')),
                ('traceback', models.TextField(blank=True, null=True, verbose_name='错误追踪')),
                ('date_created', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('date_done', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': 'Celery任务',
                'verbose_name_plural': 'Celery任务',
                'db_table': 'celery_task',
                'indexes': [models.Index(fields=['task_id'], name='celery_task_task_id_6982ef_idx'), models.Index(fields=['status'], name='celery_task_status_a7bed6_idx'), models.Index(fields=['date_created'], name='celery_task_date_cr_1d4346_idx')],
            },
        ),
    ]
