# 图书馆自习室管理系统 - 总体设计方案

## 一、项目概述

### 1.1 项目背景

图书馆自习室管理系统旨在解决高校图书馆自习室座位管理的痛点问题，包括"占座"现象、座位利用率低、管理效率不高等问题。通过引入国密算法（SM2/SM3/SM4）保障系统安全，实现座位预约、签到、管理等功能，提高自习室资源利用率，改善学生学习体验。

### 1.2 系统目标

1. **功能目标**：实现自习室座位预约、签到、管理等核心功能
2. **安全目标**：采用国密算法保障用户信息安全和系统数据完整性
3. **性能目标**：支持50-100用户并发访问，响应时间控制在3秒内
4. **用户体验**：提供直观、便捷的操作界面，降低使用门槛

### 1.3 需求分析

#### 1.3.1 用户需求

- 学生用户需要能够便捷地查看、预约、取消座位
- 学生用户需要能够通过扫码快速签到
- 学生用户需要了解自己的预约历史和信誉分状态
- 管理员需要能够管理自习室、座位和用户信息
- 管理员需要能够查看统计数据和系统日志

#### 1.3.2 安全需求

- 保护用户个人信息和账号安全
- 确保预约数据不被篡改
- 防止恶意攻击和未授权访问
- 确保系统日志的完整性和不可篡改性
- 符合国家密码应用安全标准

## 二、系统架构设计

### 2.1 整体架构

系统采用经典的三层架构设计：
移动端采用微信小程序，web前端采用网页
```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│    用户层       │      │    业务层       │      │    数据层       │
├─────────────────┤      ├─────────────────┤      ├─────────────────┤
│ - Web前端       │      │ - API服务       │      │ - MySQL数据库   │
│ - 移动端        │◄────►│ - 业务逻辑      │◄────►│ - 本地缓存     │
│ - 管理后台      │      │ - 安全控制      │      │ - 文件存储      │
└─────────────────┘      └─────────────────┘      └─────────────────┘
                                  ▲
                                  │
                         ┌────────┴───────┐
                         │   基础服务层   │
                         ├────────────────┤
                         │ - 国密算法     │
                         │ - 日志服务     │
                         │ - 消息队列     │
                         │ - 定时任务     │
                         └────────────────┘
```

### 2.2 技术栈选择

| 层级 | 技术选择 | 说明 |
|------|----------|------|
| 前端 | Vue 3 + Element Plus | 现代化UI框架，组件丰富 |
| 后端 | Django 4.x + DRF | 成熟稳定的Python Web框架 |
| 数据库 | MySQL 8.x | 高性能关系型数据库 |
| 缓存 | 本地缓存 | 高性能缓存和消息队列 |
| 国密算法 | gmssl (后端)、sm-crypto (前端) | 国家密码管理局认证的国密算法库 |
| 实时通信 | Django Channels + WebSocket | 实时座位状态推送 |
| 任务调度 | Celery + Redis | 异步任务处理 |

### 2.3 模块划分

1. **核心模块**
   - 用户认证模块 (apps.authentication)
   - 座位管理模块 (apps.seat)
   - 日志管理模块 (apps.log)
   - 管理员模块 (apps.admin)

2. **基础服务模块**
   - 国密算法服务 (utils.crypto)
   - 消息推送服务 (services.notification)
   - 定时任务服务 (services.scheduler)
   - 统计分析服务 (services.statistics)

3. **前端模块**
   - 用户界面 (views/user)
   - 管理界面 (views/admin)
   - 公共组件 (components)
   - 工具函数 (utils)

### 2.4 数据流图

```
用户注册流程:
前端 → SM3哈希密码 → 发送注册请求 → 后端验证学号唯一性 → SM4加密敏感字段 → 存储用户信息

座位预约流程:
用户选择座位 → 选择时间段 → 系统检测冲突 → SM4加密预约数据 → 创建预约记录 → WebSocket推送状态更新

签到流程:
用户请求签到码 → 生成带SM3签名的二维码 → 用户扫码签到 → 验证签名和有效期 → 更新预约状态

日志记录流程:
系统事件发生 → 生成日志记录 → 计算当前记录SM3哈希 → 与前一条记录哈希组合 → 生成哈希链 → 存储日志
```

## 三、核心功能概述

### 3.1 用户身份认证

- 支持学号密码注册和登录
- 支持SM2证书认证
- JWT令牌认证机制
- 用户信息管理与信誉分系统
- 管理员双因素认证

### 3.2 座位管理

- 自习室与座位的管理
- 实时座位状态显示
- 座位预约与冲突检测
- 签到与座位释放机制
- 违规记录与黑名单管理

### 3.3 日志与安全

- 操作日志记录与查询
- SM3哈希链确保日志完整性
- 敏感数据SM4加密存储
- 通信数据加密传输
- 安全审计与异常检测

### 3.4 统计与分析

- 座位使用率统计
- 用户活跃度分析
- 违规行为统计
- 性能监控与报警

## 四、安全策略设计

### 4.1 密码安全策略

- 密码通过SM3哈希存储
- 引入密码盐值和迭代哈希
- 密码强度要求与校验
- 登录失败次数限制

### 4.2 数据安全策略

- 敏感字段SM4加密存储
- 定期密钥轮换机制
- 数据访问权限控制
- 数据备份与恢复机制

### 4.3 通信安全策略

- HTTPS加密传输
- API请求签名验证
- WebSocket通信加密
- 防重放攻击机制

### 4.4 运行安全策略

- 系统异常监控与告警
- 定期安全审计
- 漏洞扫描与修复
- 资源访问控制

## 五、项目实施计划

### 5.1 开发阶段规划

1. **准备阶段**（2周）
   - 环境搭建与技术调研
   - 详细设计文档编写
   - 开发规范制定

2. **核心模块开发**（6周）
   - 用户认证模块（2周）
   - 座位管理模块（3周）
   - 日志管理模块（1周）

3. **前端开发**（4周）
   - 用户界面开发（2周）
   - 管理界面开发（2周）

4. **测试与优化**（2周）
   - 功能测试
   - 性能测试
   - 安全测试

5. **部署上线**（1周）
   - 环境配置
   - 数据迁移
   - 部署监控

### 5.2 测试策略

- 功能测试：验证各模块功能正确性
- 安全测试：验证国密算法实现的正确性与安全性
- 性能测试：模拟50-100用户并发访问
- 用户体验测试：收集用户反馈优化界面

### 5.3 部署策略

- 开发环境：本地开发服务器
- 测试环境：与生产环境配置相同的测试服务器

## 六、系统评估指标

### 6.1 功能指标

- 核心功能完整实现率：100%
- 用户操作步骤：不超过3步
- 管理功能覆盖率：≥95%

### 6.2 性能指标

- 系统响应时间：<3秒
- 并发用户支持：50-100用户
- 数据库查询效率：≤500ms
- WebSocket实时推送延迟：≤1秒

### 6.3 安全指标

- 敏感数据加密率：100%
- 日志完整性校验通过率：100%
- 密码安全等级：中等及以上
- 安全漏洞：零关键漏洞

### 6.4 可用性指标

- 系统可用性：≥99.9%
- 数据备份恢复时间：≤1小时
- 用户界面兼容性：支持主流浏览器

## 七、附录

### 7.1 相关文档

- 数据库设计说明
- API接口文档
- 部署文档
- 用户手册
- 管理员手册

### 7.2 参考资料

- 国密算法标准文档
- Django框架文档
- Vue.js框架文档
- 相关法规和标准 

