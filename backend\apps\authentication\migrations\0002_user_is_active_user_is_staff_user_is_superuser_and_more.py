# Generated by Django 4.2.7 on 2025-05-21 07:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='是否激活'),
        ),
        migrations.AddField(
            model_name='user',
            name='is_staff',
            field=models.BooleanField(default=False, verbose_name='是否为管理员'),
        ),
        migrations.AddField(
            model_name='user',
            name='is_superuser',
            field=models.BooleanField(default=False, verbose_name='是否为超级用户'),
        ),
        migrations.AlterField(
            model_name='user',
            name='status',
            field=models.CharField(default='active', max_length=20, verbose_name='状态(active/disabled/blacklisted/locked)'),
        ),
    ]
