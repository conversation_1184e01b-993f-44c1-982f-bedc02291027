"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-rpc3@3y)pgp-*73*+=d=4szze2f!cx9a29xwi68o%7-w26xre)'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # 第三方应用
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'channels',
    'django_celery_beat',     # Celery定时任务

    # 自定义应用
    'apps.authentication',
    'apps.seat',
    'apps.log',
    'apps.admin_management',
    'apps.crypto',
    'utils',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # CORS中间件
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'utils.middleware.EncryptionMiddleware',  # 加密请求中间件
    'utils.middleware.LoggingMiddleware',  # API请求日志中间件
]

ROOT_URLCONF = 'backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'backend.wsgi.application'
ASGI_APPLICATION = 'backend.asgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# MySQL配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'seat_management',
        'USER': 'root',
        'PASSWORD': '123456',  # 您提供的MySQL密码
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 配置PyMySQL作为MySQL数据库连接器
import pymysql
pymysql.install_as_MySQLdb()


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# REST Framework 配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'EXCEPTION_HANDLER': 'utils.exception_handler.custom_exception_handler',
}

# JWT 配置
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
}

# CORS 配置
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]
CORS_ALLOW_METHODS = [
    'GET',
    'POST',
    'PUT',
    'PATCH',
    'DELETE',
    'OPTIONS',
]
CORS_ALLOW_HEADERS = [
    'Authorization',
    'Content-Type',
    'X-Request-ID',
    'X-Timestamp',
    'X-Nonce',
    'X-Signature',
    'X-Encrypted',
]

# 国密算法配置
SM2_PUBLIC_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'sm2_public_key.pem')
SM2_PRIVATE_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'sm2_private_key.pem')

# 创建密钥目录
os.makedirs(os.path.dirname(SM2_PUBLIC_KEY_PATH), exist_ok=True)

# 认证配置
AUTHENTICATION_BACKENDS = [
    'apps.authentication.backends.SM3PasswordBackend',
    'apps.authentication.backends.SM2CertificateBackend',
    'apps.admin_management.backends.AdminBackend',  # 添加管理员认证后端
    'django.contrib.auth.backends.ModelBackend',
]

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# Celery配置 - 使用内存作为消息代理
CELERY_BROKER_URL = 'memory://'
CELERY_RESULT_BACKEND = 'cache'
CELERY_CACHE_BACKEND = 'default'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_BROKER_TRANSPORT_OPTIONS = {
    'max_retries': 3,
    'interval_start': 0,
    'interval_step': 0.2,
    'interval_max': 0.5,
}

# Celery Beat配置
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers.DatabaseScheduler'

# Celery定时任务配置
from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    # 座位管理相关任务
    'process-reservation-timeout': {
        'task': 'apps.seat.tasks.process_reservation_timeout',
        'schedule': crontab(minute='*/10'),  # 每10分钟执行一次
    },
    'process-auto-checkout': {
        'task': 'apps.seat.tasks.process_auto_checkout',
        'schedule': crontab(minute='*/15'),  # 每15分钟执行一次
    },
    'process-blacklist-status': {
        'task': 'apps.seat.tasks.process_blacklist_status',
        'schedule': crontab(hour='0', minute='0'),  # 每天凌晨执行
    },
    'verify-seat-operations': {
        'task': 'apps.seat.tasks.verify_seat_operations',
        'schedule': crontab(hour='1', minute='0'),  # 每天凌晨1点执行
    },

    # 用户认证相关任务
    'process-expired-sessions': {
        'task': 'apps.seat.tasks.process_expired_sessions',
        'schedule': crontab(minute='*/30'),  # 每30分钟执行一次
    },
    'process-locked-accounts': {
        'task': 'apps.seat.tasks.process_locked_accounts',
        'schedule': crontab(hour='0', minute='30'),  # 每天凌晨0:30执行
    },
    'process-credit-score-restoration': {
        'task': 'apps.seat.tasks.process_credit_score_restoration',
        'schedule': crontab(hour='1', minute='30'),  # 每天凌晨1:30执行
    },

    # 日志验证和审计任务
    'verify-system-log-chain': {
        'task': 'apps.log.tasks.verify_system_log_chain',
        'schedule': crontab(hour='2', minute='0'),  # 每天凌晨2点执行
    },
    'verify-user-action-log-chain': {
        'task': 'apps.log.tasks.verify_user_action_log_chain',
        'schedule': crontab(hour='2', minute='15'),  # 每天凌晨2:15执行
    },
    'verify-security-log-chain': {
        'task': 'apps.log.tasks.verify_security_log_chain',
        'schedule': crontab(hour='2', minute='30'),  # 每天凌晨2:30执行
    },
    'verify-api-request-log-chain': {
        'task': 'apps.log.tasks.verify_api_request_log_chain',
        'schedule': crontab(hour='2', minute='45'),  # 每天凌晨2:45执行
    },
    'clean-old-logs': {
        'task': 'apps.log.tasks.clean_old_logs',
        'schedule': crontab(day_of_month='1', hour='3', minute='0'),  # 每月1日凌晨3点执行
    },
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'authentication': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'seat': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'utils': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'crypto': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
