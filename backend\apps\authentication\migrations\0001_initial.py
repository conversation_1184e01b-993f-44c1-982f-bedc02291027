# Generated by Django 4.2.7 on 2025-05-21 05:17

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CreditRecord',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('delta', models.IntegerField(verbose_name='变动分值')),
                ('reason', models.CharField(max_length=255, verbose_name='变动原因')),
                ('score_after', models.IntegerField(verbose_name='变动后分数')),
                ('operator_id', models.BigIntegerField(blank=True, null=True, verbose_name='操作员ID（管理员操作）')),
                ('operator_type', models.CharField(default='system', max_length=10, verbose_name='操作员类型(system/admin)')),
                ('related_entity', models.CharField(blank=True, max_length=20, null=True, verbose_name='相关实体(reservation/check_in)')),
                ('related_id', models.BigIntegerField(blank=True, null=True, verbose_name='相关实体ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '信誉分记录',
                'verbose_name_plural': '信誉分记录',
                'db_table': 'credit_record',
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('student_id', models.BinaryField(max_length=64, verbose_name='SM4加密的学号')),
                ('student_id_hash', models.CharField(max_length=64, unique=True, verbose_name='SM3哈希的学号(用于索引)')),
                ('password', models.CharField(max_length=64, verbose_name='SM3哈希的密码')),
                ('salt', models.CharField(blank=True, max_length=32, null=True, verbose_name='密码盐值')),
                ('iterations', models.IntegerField(default=10000, verbose_name='密码哈希迭代次数')),
                ('public_key', models.TextField(blank=True, null=True, verbose_name='SM2公钥')),
                ('public_key_expires', models.DateTimeField(blank=True, null=True, verbose_name='公钥过期时间')),
                ('email', models.BinaryField(blank=True, max_length=128, null=True, verbose_name='SM4加密的邮箱')),
                ('phone', models.BinaryField(blank=True, max_length=64, null=True, verbose_name='SM4加密的手机号')),
                ('status', models.CharField(default='active', max_length=20, verbose_name='状态(active/disabled/blacklisted)')),
                ('credit_score', models.IntegerField(default=100, verbose_name='信誉分')),
                ('login_attempts', models.IntegerField(default=0, verbose_name='登录失败次数')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'user',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('token', models.CharField(max_length=64, verbose_name='JWT令牌ID')),
                ('ip_address', models.CharField(max_length=45, verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户ID')),
            ],
            options={
                'verbose_name': '用户会话',
                'verbose_name_plural': '用户会话',
                'db_table': 'user_session',
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['status'], name='user_status_116710_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['credit_score'], name='user_credit__437afa_idx'),
        ),
        migrations.AddField(
            model_name='creditrecord',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.user', verbose_name='用户ID'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['user'], name='user_sessio_user_id_c6fc39_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['token'], name='user_sessio_token_8c44e0_idx'),
        ),
        migrations.AddIndex(
            model_name='usersession',
            index=models.Index(fields=['expires_at'], name='user_sessio_expires_85bda6_idx'),
        ),
        migrations.AddIndex(
            model_name='creditrecord',
            index=models.Index(fields=['user'], name='credit_reco_user_id_cfa4bf_idx'),
        ),
        migrations.AddIndex(
            model_name='creditrecord',
            index=models.Index(fields=['created_at'], name='credit_reco_created_77b776_idx'),
        ),
    ]
