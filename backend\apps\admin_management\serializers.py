from rest_framework import serializers
from .models import Admin, AdminSession, SystemConfig, AdminOperationLog
from utils.crypto import SM3Hasher, SM4Crypto
import binascii
import secrets
import base64

class AdminLoginSerializer(serializers.Serializer):
    """管理员登录序列化器"""
    username = serializers.CharField(max_length=50, required=True, help_text='用户名')
    password = serializers.CharField(max_length=64, required=True, write_only=True, help_text='密码')

    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        try:
            admin = Admin.objects.get(username=username)
        except Admin.DoesNotExist:
            raise serializers.ValidationError({'username': '用户名或密码错误'})
        
        # 检查管理员状态
        if admin.status != 'active':
            raise serializers.ValidationError({'username': f'账号状态异常: {admin.status}'})
        
        # 验证密码
        if not SM3Hasher.verify(password, admin.password, admin.salt, admin.iterations):
            raise serializers.ValidationError({'password': '用户名或密码错误'})
        
        # 将管理员对象添加到验证后的数据中
        attrs['admin'] = admin
        return attrs


class AdminSerializer(serializers.ModelSerializer):
    """管理员序列化器"""
    class Meta:
        model = Admin
        fields = ['id', 'username', 'role', 'status', 'last_login', 'created_at']
        read_only_fields = ['id', 'username', 'role', 'last_login', 'created_at']


class AdminDetailSerializer(serializers.ModelSerializer):
    """管理员详情序列化器"""
    email = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    
    class Meta:
        model = Admin
        fields = ['id', 'username', 'role', 'email', 'phone', 'status', 
                  'last_login', 'created_at', 'updated_at']
        read_only_fields = ['id', 'username', 'role', 'last_login', 
                           'created_at', 'updated_at']
    
    def get_email(self, obj):
        if not obj.email:
            return None
        
        # 实际应用中应该使用系统配置的密钥
        sm4_key = SM4Crypto.generate_key()
        try:
            encrypted_email = binascii.hexlify(obj.email).decode()
            return SM4Crypto.decrypt(sm4_key, encrypted_email)
        except Exception:
            return None
    
    def get_phone(self, obj):
        if not obj.phone:
            return None
        
        # 实际应用中应该使用系统配置的密钥
        sm4_key = SM4Crypto.generate_key()
        try:
            encrypted_phone = binascii.hexlify(obj.phone).decode()
            return SM4Crypto.decrypt(sm4_key, encrypted_phone)
        except Exception:
            return None


class AdminUpdateSerializer(serializers.ModelSerializer):
    """管理员信息更新序列化器"""
    password = serializers.CharField(max_length=64, required=False, write_only=True)
    old_password = serializers.CharField(max_length=64, required=False, write_only=True)
    email = serializers.EmailField(required=False)
    phone = serializers.CharField(max_length=20, required=False)
    
    class Meta:
        model = Admin
        fields = ['password', 'old_password', 'email', 'phone', 'status']
    
    def validate(self, attrs):
        # 如果要更新密码，需要验证旧密码
        if 'password' in attrs and 'old_password' not in attrs:
            raise serializers.ValidationError({'old_password': '更新密码需要提供旧密码'})
        
        if 'password' in attrs and 'old_password' in attrs:
            admin = self.instance
            old_password = attrs.pop('old_password')
            
            # 验证旧密码
            if not SM3Hasher.verify(old_password, admin.password, admin.salt, admin.iterations):
                raise serializers.ValidationError({'old_password': '旧密码错误'})
        
        return attrs
    
    def update(self, instance, validated_data):
        # 生成SM4密钥（实际应用中应该使用系统配置的密钥）
        sm4_key = SM4Crypto.generate_key()
        
        # 更新密码
        if 'password' in validated_data:
            password = validated_data.pop('password')
            password_hash_result = SM3Hasher.hash_with_salt(password)
            instance.password = password_hash_result['hash']
            instance.salt = password_hash_result['salt']
            instance.iterations = password_hash_result['iterations']
        
        # 更新邮箱
        if 'email' in validated_data:
            email = validated_data.pop('email')
            encrypted_email = SM4Crypto.encrypt(sm4_key, email)
            instance.email = binascii.unhexlify(encrypted_email)
        
        # 更新手机号
        if 'phone' in validated_data:
            phone = validated_data.pop('phone')
            encrypted_phone = SM4Crypto.encrypt(sm4_key, phone)
            instance.phone = binascii.unhexlify(encrypted_phone)
        
        # 更新状态
        if 'status' in validated_data:
            instance.status = validated_data.pop('status')
        
        instance.save()
        return instance


class SystemConfigSerializer(serializers.ModelSerializer):
    """系统配置序列化器"""
    class Meta:
        model = SystemConfig
        fields = ['id', 'key', 'value', 'description', 'is_encrypted', 
                  'created_at', 'updated_at', 'updated_by']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # 如果配置需要加密
        if validated_data.get('is_encrypted', False):
            # 实际应用中应该使用系统配置的密钥
            sm4_key = SM4Crypto.generate_key()
            value = validated_data.get('value')
            encrypted_value = SM4Crypto.encrypt(sm4_key, value)
            validated_data['value'] = encrypted_value
        
        # 设置更新人
        validated_data['updated_by'] = self.context['request'].user
        
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        # 如果配置需要加密
        if validated_data.get('is_encrypted', instance.is_encrypted):
            # 实际应用中应该使用系统配置的密钥
            sm4_key = SM4Crypto.generate_key()
            value = validated_data.get('value', instance.value)
            encrypted_value = SM4Crypto.encrypt(sm4_key, value)
            validated_data['value'] = encrypted_value
        
        # 设置更新人
        validated_data['updated_by'] = self.context['request'].user
        
        return super().update(instance, validated_data)


class AdminOperationLogSerializer(serializers.ModelSerializer):
    """管理员操作日志序列化器"""
    admin_username = serializers.CharField(source='admin.username', read_only=True)
    
    class Meta:
        model = AdminOperationLog
        fields = ['id', 'admin', 'admin_username', 'operation', 'description', 
                  'ip_address', 'target_type', 'target_id', 'status', 'created_at']
        read_only_fields = fields
