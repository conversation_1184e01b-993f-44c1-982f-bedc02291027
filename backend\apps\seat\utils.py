"""
座位管理工具函数
"""
import logging
import datetime
from django.utils import timezone
from django.db.models import Q
from .models import Room, Seat, Reservation, SeatOperation, BlacklistRecord
from apps.authentication.utils import update_credit_score, get_client_ip
from utils.crypto import SM3Hasher

logger = logging.getLogger(__name__)

def check_seat_availability(seat, start_time, end_time):
    """
    检查座位在指定时间段内是否可用
    
    参数:
        seat: 座位对象
        start_time: 开始时间
        end_time: 结束时间
        
    返回:
        (是否可用, 原因)
    """
    # 检查座位状态
    if seat.status == 'disabled':
        return False, "座位已禁用"
    
    # 检查自习室状态
    room = seat.room
    if room.status != 'open':
        return False, f"自习室状态: {room.status}"
    
    # 检查是否在开放时间内
    start_date = start_time.date()
    end_date = end_time.date()
    
    # 如果跨天，暂不支持
    if start_date != end_date:
        return False, "不支持跨天预约"
    
    # 检查是否在自习室开放时间内
    room_open_time = datetime.datetime.combine(
        start_date, 
        room.open_time, 
        tzinfo=timezone.get_current_timezone()
    )
    room_close_time = datetime.datetime.combine(
        start_date, 
        room.close_time, 
        tzinfo=timezone.get_current_timezone()
    )
    
    if start_time < room_open_time:
        return False, f"预约开始时间早于自习室开放时间: {room.open_time}"
    
    if end_time > room_close_time:
        return False, f"预约结束时间晚于自习室关闭时间: {room.close_time}"
    
    # 检查是否与其他预约冲突
    conflicting_reservations = Reservation.objects.filter(
        seat=seat,
        status__in=['pending', 'checked_in'],
        start_time__lt=end_time,
        end_time__gt=start_time
    )
    
    if conflicting_reservations.exists():
        return False, "该时间段内座位已被预约"
    
    return True, "座位可用"


def create_seat_operation(seat, operation_type, user, reservation=None, ip_address=None):
    """
    创建座位操作记录
    
    参数:
        seat: 座位对象
        operation_type: 操作类型
        user: 用户对象
        reservation: 预约对象
        ip_address: IP地址
        
    返回:
        操作记录对象
    """
    # 获取当前座位状态
    status_before = seat.status
    
    # 确定操作后状态
    status_map = {
        'reserve': 'reserved',
        'check_in': 'occupied',
        'check_out': 'available',
        'cancel': 'available',
        'timeout': 'available',
        'disable': 'disabled',
        'enable': 'available'
    }
    status_after = status_map.get(operation_type, status_before)
    
    # 生成哈希值
    hash_data = f"{seat.id}_{user.id if user else 'system'}_{operation_type}_{timezone.now().isoformat()}"
    hash_value = SM3Hasher.hash(hash_data)
    
    # 获取前一条记录的哈希值
    prev_hash = None
    last_operation = SeatOperation.objects.filter(seat=seat).order_by('-created_at').first()
    if last_operation:
        prev_hash = last_operation.hash_value
    
    # 创建操作记录
    operation = SeatOperation.objects.create(
        seat=seat,
        operation_type=operation_type,
        user=user,
        reservation=reservation,
        status_before=status_before,
        status_after=status_after,
        ip_address=ip_address,
        hash_value=hash_value,
        prev_hash=prev_hash
    )
    
    # 更新座位状态
    seat.status = status_after
    seat.save(update_fields=['status'])
    
    return operation


def handle_reservation_timeout():
    """
    处理超时未签到的预约
    
    返回:
        处理的预约数量
    """
    now = timezone.now()
    # 获取已经开始但未签到且超过30分钟的预约
    timeout_threshold = now - datetime.timedelta(minutes=30)
    
    timeout_reservations = Reservation.objects.filter(
        status='pending',
        start_time__lt=timeout_threshold,
        end_time__gt=now
    )
    
    count = 0
    for reservation in timeout_reservations:
        # 更新预约状态
        reservation.status = 'timeout'
        reservation.save(update_fields=['status'])
        
        # 创建座位操作记录
        create_seat_operation(
            seat=reservation.seat,
            operation_type='timeout',
            user=reservation.user,
            reservation=reservation
        )
        
        # 扣除信誉分
        update_credit_score(
            user=reservation.user,
            delta=-10,
            reason="预约超时未签到",
            related_entity='reservation',
            related_id=reservation.id
        )
        
        count += 1
        logger.info(f"预约 {reservation.id} 超时未签到，已自动取消")
    
    return count


def handle_reservation_auto_checkout():
    """
    处理自动签退
    
    返回:
        处理的预约数量
    """
    now = timezone.now()
    
    # 获取已经结束但未签退的预约
    checkout_reservations = Reservation.objects.filter(
        status='checked_in',
        end_time__lt=now
    )
    
    count = 0
    for reservation in checkout_reservations:
        # 更新预约状态
        reservation.status = 'completed'
        reservation.check_out_time = now
        reservation.save(update_fields=['status', 'check_out_time'])
        
        # 创建座位操作记录
        create_seat_operation(
            seat=reservation.seat,
            operation_type='check_out',
            user=reservation.user,
            reservation=reservation
        )
        
        count += 1
        logger.info(f"预约 {reservation.id} 已自动签退")
    
    return count


def check_blacklist_status():
    """
    检查黑名单状态
    
    返回:
        处理的黑名单记录数量
    """
    now = timezone.now()
    
    # 获取已过期但仍处于活跃状态的黑名单记录
    expired_records = BlacklistRecord.objects.filter(
        status='active',
        end_time__lt=now
    )
    
    count = 0
    for record in expired_records:
        # 更新黑名单状态
        record.status = 'expired'
        record.save(update_fields=['status'])
        
        # 检查用户是否还有其他活跃的黑名单记录
        active_records = BlacklistRecord.objects.filter(
            user=record.user,
            status='active'
        ).exists()
        
        # 如果没有其他活跃记录，恢复用户状态
        if not active_records and record.user.status == 'blacklisted':
            record.user.status = 'active'
            record.user.save(update_fields=['status'])
            
            logger.info(f"用户 {record.user.student_id_hash} 已从黑名单中移除")
        
        count += 1
    
    return count


def verify_seat_operation_chain(seat):
    """
    验证座位操作记录链的完整性
    
    参数:
        seat: 座位对象
        
    返回:
        (是否有效, 错误信息)
    """
    operations = SeatOperation.objects.filter(seat=seat).order_by('created_at')
    
    if not operations.exists():
        return True, None
    
    prev_hash = None
    for operation in operations:
        # 第一条记录没有前置哈希
        if prev_hash is None:
            if operation.prev_hash is not None:
                return False, f"首条记录不应有前置哈希: {operation.id}"
        # 其他记录的前置哈希应与前一条记录的哈希值匹配
        elif operation.prev_hash != prev_hash:
            return False, f"记录 {operation.id} 的前置哈希不匹配"
        
        # 更新前置哈希
        prev_hash = operation.hash_value
    
    return True, None
