"""
中间件模块
包含加密请求中间件和日志中间件
"""
import json
import time
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from utils.crypto_utils import CryptoUtils

# 导入日志工具类，如果导入失败，则提供一个空的实现
try:
    from apps.log.utils import add_api_request_log
except ImportError:
    def add_api_request_log(request, response, response_time):
        pass

logger = logging.getLogger(__name__)

class EncryptionMiddleware:
    """加密请求中间件"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 处理请求
        if self._is_encrypted_request(request):
            try:
                # 解密请求数据
                self._decrypt_request(request)
            except Exception as e:
                logger.error(f"解密请求失败: {str(e)}")
                return JsonResponse({
                    'error': '解密请求失败',
                    'detail': str(e)
                }, status=400)

        # 获取响应
        response = self.get_response(request)

        # 处理响应
        if self._should_encrypt_response(request, response):
            try:
                # 加密响应数据
                self._encrypt_response(response)
            except Exception as e:
                logger.error(f"加密响应失败: {str(e)}")
                return JsonResponse({
                    'error': '加密响应失败',
                    'detail': str(e)
                }, status=500)

        return response

    def _is_encrypted_request(self, request):
        """判断是否为加密请求"""
        return (
            request.method in ['POST', 'PUT', 'PATCH'] and
            request.headers.get('X-Encrypted') == 'true' and
            request.content_type == 'application/json'
        )

    def _decrypt_request(self, request):
        """解密请求数据"""
        if not request.body:
            return

        # 解析加密包
        encrypted_package = json.loads(request.body)

        # 解密数据
        decrypted_data = CryptoUtils.decrypt_from_transmission(encrypted_package)

        # 替换请求体
        request._body = json.dumps(decrypted_data).encode('utf-8')

    def _should_encrypt_response(self, request, response):
        """判断是否需要加密响应"""
        return (
            request.headers.get('X-Encrypted') == 'true' and
            response.get('Content-Type', '').startswith('application/json')
        )

    def _encrypt_response(self, response):
        """加密响应数据"""
        if not hasattr(response, 'content'):
            return

        # 解析响应数据
        try:
            response_data = json.loads(response.content)
        except json.JSONDecodeError:
            return

        # 加密响应数据
        encrypted_package = CryptoUtils.encrypt_for_transmission(response_data)

        # 替换响应内容
        response.content = json.dumps(encrypted_package).encode('utf-8')

        # 添加加密标记头
        response['X-Encrypted'] = 'true'


class LoggingMiddleware(MiddlewareMixin):
    """API请求日志中间件"""

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response

    def process_request(self, request):
        # 记录请求开始时间
        request.start_time = time.time()
        return None

    def process_response(self, request, response):
        # 计算响应时间(毫秒)
        if hasattr(request, 'start_time'):
            response_time = int((time.time() - request.start_time) * 1000)
        else:
            response_time = 0

        # 记录API请求日志
        # 排除静态文件和管理后台请求
        if not request.path.startswith('/static/') and not request.path.startswith('/admin/'):
            add_api_request_log(request, response, response_time)

        return response
