from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.authentication.models import User
import logging
import os
import json
import secrets

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '为现有用户生成SM2密钥对并保存到文件'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=5, help='要生成的密钥对数量')
        parser.add_argument('--output-file', type=str, default='user_keys.json', help='将生成的密钥对保存到文件')

    def handle(self, *args, **options):
        count = options['count']
        output_file = options['output_file']
        
        self.stdout.write(f'开始生成 {count} 个SM2密钥对...')
        
        # 获取所有用户
        users = User.objects.all()[:count]
        
        if not users.exists():
            self.stdout.write(self.style.WARNING('没有找到用户'))
            return
        
        # 生成密钥对
        key_pairs = []
        for user in users:
            # 生成64字节的随机私钥（十六进制字符串）
            private_key = secrets.token_hex(32)
            
            # 生成随机公钥（实际应用中应该基于私钥生成，这里简化处理）
            # 公钥格式：04 + x坐标(32字节) + y坐标(32字节)
            public_key = "04" + secrets.token_hex(64)
            
            key_pairs.append({
                "student_id_hash": user.student_id_hash,
                "private_key": private_key,
                "public_key": public_key
            })
            
            # 更新用户公钥
            user.public_key = public_key
            # 设置公钥过期时间（1年后）
            user.public_key_expires = timezone.now() + timezone.timedelta(days=365)
            user.save()
            
            self.stdout.write(f'为用户 {user.student_id_hash} 生成了SM2密钥对')
        
        # 保存密钥对到文件
        with open(output_file, 'w') as f:
            json.dump(key_pairs, f, indent=2)
        
        self.stdout.write(self.style.SUCCESS(f'成功生成 {len(key_pairs)} 个SM2密钥对并保存到 {output_file}'))
