<template>
  <div class="reservation-detail">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
        <h2>预约详情</h2>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="!reservation" class="empty-container">
      <el-empty description="未找到预约信息" />
    </div>

    <div v-else class="detail-content">
      <el-card shadow="hover" class="detail-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
            <el-tag :type="getReservationStatusType(reservation.status)">
              {{ getReservationStatusText(reservation.status) }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="预约号">
            {{ reservation.id }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(reservation.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="预约时间" :span="2">
            {{ formatDateTime(reservation.start_time) }} 至
            {{ formatDateTime(reservation.end_time) }}
            <span class="duration">
              ({{
                calculateDuration(reservation.start_time, reservation.end_time)
              }})
            </span>
          </el-descriptions-item>
          <el-descriptions-item
            label="签到时间"
            v-if="reservation.check_in_time"
          >
            {{ formatDateTime(reservation.check_in_time) }}
          </el-descriptions-item>
          <el-descriptions-item
            label="签退时间"
            v-if="reservation.check_out_time"
          >
            {{ formatDateTime(reservation.check_out_time) }}
          </el-descriptions-item>
          <el-descriptions-item
            label="取消原因"
            v-if="
              reservation.status === 'cancelled' && reservation.cancel_reason
            "
            :span="2"
          >
            {{ reservation.cancel_reason }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card shadow="hover" class="detail-card">
        <template #header>
          <div class="card-header">
            <h3>座位信息</h3>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="自习室">
            {{ reservation.seat?.room?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="位置">
            {{ reservation.seat?.room?.location }}
          </el-descriptions-item>
          <el-descriptions-item label="座位号">
            {{ reservation.seat?.seat_number }}
          </el-descriptions-item>
          <el-descriptions-item label="座位类型">
            <el-tag
              v-if="reservation.seat?.is_window_seat"
              size="small"
              effect="plain"
              style="margin-right: 5px"
            >
              靠窗
            </el-tag>
            <el-tag
              v-if="reservation.seat?.is_power_outlet"
              size="small"
              effect="plain"
            >
              有电源
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="座位描述" :span="2">
            {{ reservation.seat?.description || "无" }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <div class="action-buttons">
        <template v-if="reservation.status === 'pending'">
          <el-button type="primary" @click="handleCheckIn">签到</el-button>
          <el-button type="danger" @click="handleCancel">取消预约</el-button>
        </template>

        <template v-else-if="reservation.status === 'checked_in'">
          <el-button type="success" @click="handleCheckOut">签退</el-button>
        </template>
      </div>
    </div>

    <!-- 取消预约对话框 -->
    <el-dialog v-model="cancelDialogVisible" title="取消预约" width="400px">
      <div class="cancel-dialog">
        <p>您确定要取消此预约吗？</p>
        <p>座位号: {{ reservation?.seat?.seat_number }}</p>
        <p>
          预约时间: {{ formatDateTime(reservation?.start_time) }} -
          {{ formatDateTime(reservation?.end_time) }}
        </p>

        <div class="cancel-warning">
          <el-alert
            title="取消预约提示"
            type="warning"
            description="距离预约开始时间不足30分钟取消，可能会影响您的信誉分。"
            show-icon
            :closable="false"
          />
        </div>

        <div class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">返回</el-button>
          <el-button type="danger" @click="confirmCancel" :loading="processing"
            >确认取消</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from "vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";

export default {
  name: "ReservationDetail",
  setup() {
    const store = useStore();
    const route = useRoute();

    const loading = ref(true);
    const processing = ref(false);
    const cancelDialogVisible = ref(false);

    // 获取预约详情
    const getReservationDetail = async () => {
      try {
        loading.value = true;
        const reservationId = route.params.id;

        if (!reservationId) {
          ElMessage.error("预约ID不能为空");
          return;
        }

        await store.dispatch("seat/getReservationById", reservationId);
      } catch (error) {
        ElMessage.error("获取预约详情失败");
      } finally {
        loading.value = false;
      }
    };

    // 预约信息
    const reservation = computed(() => {
      return store.getters["seat/currentReservation"];
    });

    // 获取预约状态类型
    const getReservationStatusType = (status) => {
      switch (status) {
        case "pending":
          return "warning";
        case "checked_in":
          return "success";
        case "completed":
          return "info";
        case "cancelled":
          return "danger";
        case "timeout":
          return "danger";
        default:
          return "info";
      }
    };

    // 获取预约状态文本
    const getReservationStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待签到";
        case "checked_in":
          return "已签到";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        case "timeout":
          return "已超时";
        default:
          return "未知状态";
      }
    };

    // 格式化日期时间
    const formatDateTime = (dateTimeString) => {
      if (!dateTimeString) return "";

      const date = new Date(dateTimeString);
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(
        date.getDate()
      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
    };

    // 计算时长
    const calculateDuration = (startTime, endTime) => {
      if (!startTime || !endTime) return "";

      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end - start;
      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      return `${diffHrs}小时${diffMins}分钟`;
    };

    // 补零
    function padZero(num) {
      return num < 10 ? `0${num}` : num;
    }

    // 处理签到
    const handleCheckIn = async () => {
      try {
        processing.value = true;

        await store.dispatch("seat/checkIn", {
          reservationId: reservation.value.id,
        });

        ElMessage.success("签到成功");
        getReservationDetail();
      } catch (error) {
        ElMessage.error(error.message || "签到失败");
      } finally {
        processing.value = false;
      }
    };

    // 处理签退
    const handleCheckOut = async () => {
      try {
        processing.value = true;

        await store.dispatch("seat/checkOut", {
          reservationId: reservation.value.id,
        });

        ElMessage.success("签退成功");
        getReservationDetail();
      } catch (error) {
        ElMessage.error(error.message || "签退失败");
      } finally {
        processing.value = false;
      }
    };

    // 处理取消预约
    const handleCancel = () => {
      cancelDialogVisible.value = true;
    };

    // 确认取消预约
    const confirmCancel = async () => {
      try {
        processing.value = true;

        await store.dispatch("seat/cancelReservation", {
          reservationId: reservation.value.id,
        });

        ElMessage.success("预约已取消");
        cancelDialogVisible.value = false;
        getReservationDetail();
      } catch (error) {
        ElMessage.error(error.message || "取消预约失败");
      } finally {
        processing.value = false;
      }
    };

    onMounted(() => {
      getReservationDetail();
    });

    return {
      loading,
      processing,
      reservation,
      cancelDialogVisible,
      getReservationStatusType,
      getReservationStatusText,
      formatDateTime,
      calculateDuration,
      handleCheckIn,
      handleCheckOut,
      handleCancel,
      confirmCancel,
      ArrowLeft,
    };
  },
};
</script>

<style lang="scss" scoped>
.reservation-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 10px;

    h2 {
      margin: 0;
    }
  }
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.detail-content {
  max-width: 800px;
  margin: 0 auto;
}

.detail-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
    }
  }
}

.duration {
  color: #909399;
  margin-left: 10px;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.cancel-dialog {
  p {
    margin: 10px 0;
  }

  .cancel-warning {
    margin: 20px 0;
  }

  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
