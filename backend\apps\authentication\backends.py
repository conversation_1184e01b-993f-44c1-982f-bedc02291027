"""
自定义用户认证后端
"""
import logging
from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import AnonymousUser
from .models import User
from utils.crypto import SM3Hasher

logger = logging.getLogger(__name__)

class SM3PasswordBackend(BaseBackend):
    """
    使用SM3哈希算法验证密码的认证后端
    """
    
    def authenticate(self, request, student_id=None, password=None, **kwargs):
        """
        验证用户凭据
        
        参数:
            request: 请求对象
            student_id: 学号
            password: 密码
            
        返回:
            验证成功返回用户对象，否则返回None
        """
        if student_id is None or password is None:
            return None
        
        try:
            # 计算学号的SM3哈希值用于查询
            student_id_hash = SM3Hasher.hash(student_id)
            
            # 查询用户
            user = User.objects.get(student_id_hash=student_id_hash)
            
            # 检查用户状态
            if user.status != 'active':
                logger.warning(f"用户 {student_id_hash} 状态异常: {user.status}")
                return None
            
            # 验证密码
            if not SM3Hasher.verify(password, user.password, user.salt, user.iterations):
                # 增加登录失败次数
                user.login_attempts += 1
                if user.login_attempts >= 5:
                    user.status = 'locked'
                    logger.warning(f"用户 {student_id_hash} 因多次登录失败被锁定")
                user.save()
                return None
            
            # 重置登录失败次数
            if user.login_attempts > 0:
                user.login_attempts = 0
                user.save()
            
            # 返回用户对象
            return user
        except User.DoesNotExist:
            logger.warning(f"尝试使用不存在的学号登录: {student_id}")
            return None
        except Exception as e:
            logger.error(f"认证过程发生错误: {str(e)}")
            return None
    
    def get_user(self, user_id):
        """
        根据用户ID获取用户对象
        
        参数:
            user_id: 用户ID
            
        返回:
            用户对象，如果不存在则返回None
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None


class SM2CertificateBackend(BaseBackend):
    """
    使用SM2证书验证的认证后端
    """
    
    def authenticate(self, request, student_id=None, signature=None, challenge=None, **kwargs):
        """
        验证用户SM2签名
        
        参数:
            request: 请求对象
            student_id: 学号
            signature: SM2签名
            challenge: 挑战值
            
        返回:
            验证成功返回用户对象，否则返回None
        """
        if student_id is None or signature is None or challenge is None:
            return None
        
        try:
            # 计算学号的SM3哈希值用于查询
            student_id_hash = SM3Hasher.hash(student_id)
            
            # 查询用户
            user = User.objects.get(student_id_hash=student_id_hash)
            
            # 检查用户状态
            if user.status != 'active':
                logger.warning(f"用户 {student_id_hash} 状态异常: {user.status}")
                return None
            
            # 检查用户是否有公钥
            if not user.public_key:
                logger.warning(f"用户 {student_id_hash} 没有注册SM2公钥")
                return None
            
            # 检查公钥是否过期
            if user.public_key_expires and user.public_key_expires < timezone.now():
                logger.warning(f"用户 {student_id_hash} 的SM2公钥已过期")
                return None
            
            # 验证签名
            from utils.crypto import SM2Crypto
            if not SM2Crypto.verify(user.public_key, challenge, signature):
                logger.warning(f"用户 {student_id_hash} 的SM2签名验证失败")
                return None
            
            # 返回用户对象
            return user
        except User.DoesNotExist:
            logger.warning(f"尝试使用不存在的学号登录: {student_id}")
            return None
        except Exception as e:
            logger.error(f"SM2认证过程发生错误: {str(e)}")
            return None
    
    def get_user(self, user_id):
        """
        根据用户ID获取用户对象
        
        参数:
            user_id: 用户ID
            
        返回:
            用户对象，如果不存在则返回None
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
