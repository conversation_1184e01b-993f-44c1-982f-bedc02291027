<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    @closed="handleClosed"
  >
    <div class="confirm-content">
      <slot>{{ message }}</slot>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ cancelButtonText }}</el-button>
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >
          {{ confirmButtonText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, watch } from "vue";

export default {
  name: "ConfirmDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "确认",
    },
    message: {
      type: String,
      default: "确定要执行此操作吗？",
    },
    width: {
      type: String,
      default: "30%",
    },
    confirmButtonText: {
      type: String,
      default: "确定",
    },
    cancelButtonText: {
      type: String,
      default: "取消",
    },
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },
    closeOnPressEscape: {
      type: Boolean,
      default: true,
    },
    showClose: {
      type: Boolean,
      default: true,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["update:visible", "confirm", "cancel", "closed"],
  setup(props, { emit }) {
    const dialogVisible = ref(props.visible);

    watch(
      () => props.visible,
      (val) => {
        dialogVisible.value = val;
      }
    );

    watch(dialogVisible, (val) => {
      emit("update:visible", val);
    });

    const handleConfirm = () => {
      emit("confirm");
    };

    const handleCancel = () => {
      dialogVisible.value = false;
      emit("cancel");
    };

    const handleClosed = () => {
      emit("closed");
    };

    return {
      dialogVisible,
      handleConfirm,
      handleCancel,
      handleClosed,
    };
  },
};
</script>

<style lang="scss" scoped>
.confirm-content {
  padding: 10px 0;
  font-size: 16px;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
