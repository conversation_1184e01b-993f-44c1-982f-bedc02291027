from django.db import migrations
import datetime

def create_initial_data(apps, schema_editor):
    Room = apps.get_model('seat', 'Room')
    Seat = apps.get_model('seat', 'Seat')
    
    # 创建自习室
    rooms = []
    
    # 图书馆共八层，1-6层为开放区域，7-8层不开放
    # 每层分东区西区，共两个自习室
    for floor in range(1, 9):
        for area_idx, area in enumerate(['东区', '西区']):
            # 7-8层不开放
            status = 'open' if floor <= 6 else 'closed'
            
            # 每个自习室座位为6×6的布局
            capacity = 36
            
            room_name = f'{floor}楼{area}自习室'
            room = Room.objects.create(
                name=room_name,
                location=f'图书馆{floor}楼{area}',
                floor=floor,
                capacity=capacity,
                open_time=datetime.time(8, 0),  # 8:00 AM
                close_time=datetime.time(22, 0),  # 10:00 PM
                status=status,
                description=f'图书馆{floor}楼{area}自习室，{"开放" if status == "open" else "不开放"}区域'
            )
            rooms.append(room)
    
    # 为每个自习室创建座位
    for room in rooms:
        # 创建一个6×6的座位网格
        for row in range(1, 7):
            for col in range(1, 7):
                seat_number = f'{row}-{col}'
                
                # 每个自习室座位靠边每隔一位有电源插座
                # 靠边的座位：第1行、第6行、第1列、第6列
                is_edge_seat = row == 1 or row == 6 or col == 1 or col == 6
                
                # 每隔一位有电源插座
                is_power_outlet = is_edge_seat and ((row + col) % 2 == 0)
                
                # 靠窗的座位：第1行和第6行
                is_window_seat = row == 1 or row == 6
                
                # 创建座位
                Seat.objects.create(
                    room=room,
                    seat_number=seat_number,
                    row=row,
                    column=col,
                    status='available' if room.status == 'open' else 'disabled',
                    is_power_outlet=is_power_outlet,
                    is_window_seat=is_window_seat
                )

def remove_initial_data(apps, schema_editor):
    Room = apps.get_model('seat', 'Room')
    Room.objects.all().delete()

class Migration(migrations.Migration):
    dependencies = [
        ('seat', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_initial_data, remove_initial_data),
    ]
