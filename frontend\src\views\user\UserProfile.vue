<template>
  <div class="user-profile">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h3>个人信息</h3>
          <el-button type="primary" @click="editMode = !editMode">
            {{ editMode ? "取消编辑" : "编辑信息" }}
          </el-button>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          :disabled="!editMode"
        >
          <el-form-item label="学号">
            <el-input v-model="userInfo.studentIdHash" disabled />
            <small class="form-hint">学号信息不可修改</small>
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" />
          </el-form-item>

          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" />
          </el-form-item>

          <el-form-item label="信誉分">
            <el-progress
              :percentage="userInfo.creditScore"
              :color="creditScoreColor"
              :format="formatCreditScore"
              :stroke-width="18"
            />
            <small class="form-hint">信誉分影响您的预约权限</small>
          </el-form-item>

          <el-form-item label="注册时间">
            <el-input v-model="userInfo.createdAt" disabled />
          </el-form-item>

          <el-form-item label="最后登录">
            <el-input v-model="userInfo.lastLogin" disabled />
          </el-form-item>

          <el-form-item v-if="editMode">
            <el-button
              type="primary"
              @click="updateProfile"
              :loading="updating"
            >
              保存修改
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h3>安全设置</h3>
        </div>
      </template>

      <div class="security-options">
        <div class="security-item">
          <div class="security-info">
            <h4>修改密码</h4>
            <p>定期修改密码可以提高账号安全性</p>
          </div>
          <el-button @click="showChangePasswordDialog">修改</el-button>
        </div>

        <div class="security-item">
          <div class="security-info">
            <h4>SM2密钥管理</h4>
            <p>管理您的SM2密钥对，用于安全登录和数据签名</p>
          </div>
          <el-button @click="showKeyManagementDialog">管理</el-button>
        </div>
      </div>
    </el-card>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="400px">
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="changePassword"
            :loading="changingPassword"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- SM2密钥管理对话框 -->
    <el-dialog v-model="keyDialogVisible" title="SM2密钥管理" width="500px">
      <div class="key-management">
        <div class="key-status">
          <h4>当前状态</h4>
          <p>
            <el-tag :type="userInfo.hasPublicKey ? 'success' : 'danger'">
              {{ userInfo.hasPublicKey ? "已设置SM2密钥" : "未设置SM2密钥" }}
            </el-tag>
            <span v-if="userInfo.publicKeyExpires">
              (过期时间: {{ userInfo.publicKeyExpires }})
            </span>
          </p>
        </div>

        <div class="key-actions">
          <el-button type="primary" @click="generateNewKeyPair"
            >生成新密钥对</el-button
          >
          <el-button
            type="danger"
            @click="removePublicKey"
            :disabled="!userInfo.hasPublicKey"
          >
            移除公钥
          </el-button>
        </div>

        <div v-if="newKeyPair.publicKey" class="new-key-pair">
          <h4>新生成的密钥对</h4>

          <div class="key-box">
            <div class="key-label">
              <span>公钥</span>
              <div class="key-actions-buttons">
                <el-button type="primary" size="small" @click="copyPublicKey">
                  <el-icon><DocumentCopy /></el-icon> 复制
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="downloadPublicKey"
                >
                  <el-icon><Download /></el-icon> 下载
                </el-button>
              </div>
            </div>
            <el-input
              v-model="newKeyPair.publicKey"
              type="textarea"
              :rows="3"
              readonly
            />
          </div>

          <div class="key-box">
            <div class="key-label">
              <span>私钥</span>
              <div class="key-actions-buttons">
                <el-button type="primary" size="small" @click="copyPrivateKey">
                  <el-icon><DocumentCopy /></el-icon> 复制
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="downloadPrivateKey"
                >
                  <el-icon><Download /></el-icon> 下载
                </el-button>
              </div>
            </div>
            <el-input
              v-model="newKeyPair.privateKey"
              type="textarea"
              :rows="3"
              readonly
              show-password
            />
          </div>

          <el-alert
            title="重要提示"
            type="warning"
            description="请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。"
            show-icon
            :closable="false"
            class="key-warning"
          />

          <div class="save-key-action">
            <el-button type="success" @click="saveNewPublicKey"
              >保存新公钥到服务器</el-button
            >
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from "vue";
import { useStore } from "vuex";
import { ElMessage, ElMessageBox } from "element-plus";
import { DocumentCopy, Download } from "@element-plus/icons-vue";
import { SM2Crypto, SM3Hasher } from "@/utils/crypto";

export default {
  name: "UserProfile",
  setup() {
    const store = useStore();
    const formRef = ref(null);
    const passwordFormRef = ref(null);

    const loading = ref(true);
    const updating = ref(false);
    const changingPassword = ref(false);
    const editMode = ref(false);

    const passwordDialogVisible = ref(false);
    const keyDialogVisible = ref(false);

    // 用户信息
    const userInfo = computed(() => store.getters["user/userInfo"]);

    // 编辑表单
    const form = reactive({
      email: "",
      phone: "",
    });

    // 修改密码表单
    const passwordForm = reactive({
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    });

    // 新密钥对
    const newKeyPair = reactive({
      publicKey: "",
      privateKey: "",
    });

    // 信誉分颜色
    const creditScoreColor = computed(() => {
      const score = userInfo.value.creditScore || 0;
      if (score >= 90) return "#67c23a";
      if (score >= 70) return "#409eff";
      if (score >= 50) return "#e6a23c";
      return "#f56c6c";
    });

    // 格式化信誉分
    const formatCreditScore = (percentage) => {
      return `${percentage}分`;
    };

    // 表单验证规则
    const rules = {
      email: [
        { required: true, message: "请输入邮箱", trigger: "blur" },
        { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
      ],
      phone: [
        { required: true, message: "请输入手机号", trigger: "blur" },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: "请输入正确的手机号格式",
          trigger: "blur",
        },
      ],
    };

    // 密码表单验证规则
    const validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else if (value.length < 6) {
        callback(new Error("密码长度不能小于6位"));
      } else {
        if (passwordForm.confirmPassword !== "") {
          passwordFormRef.value.validateField("confirmPassword");
        }
        callback();
      }
    };

    const validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== passwordForm.newPassword) {
        callback(new Error("两次输入密码不一致"));
      } else {
        callback();
      }
    };

    const passwordRules = {
      oldPassword: [
        { required: true, message: "请输入当前密码", trigger: "blur" },
      ],
      newPassword: [{ validator: validatePass, trigger: "blur" }],
      confirmPassword: [{ validator: validatePass2, trigger: "blur" }],
    };

    // 获取用户信息
    const getUserInfo = async () => {
      try {
        loading.value = true;
        await store.dispatch("user/getUserInfo");

        // 填充表单
        form.email = userInfo.value.email || "";
        form.phone = userInfo.value.phone || "";
      } catch (error) {
        ElMessage.error("获取用户信息失败");
      } finally {
        loading.value = false;
      }
    };

    // 更新个人信息
    const updateProfile = async () => {
      if (!formRef.value) return;

      await formRef.value.validate(async (valid) => {
        if (valid) {
          try {
            updating.value = true;
            await store.dispatch("user/updateProfile", form);
            ElMessage.success("个人信息更新成功");
            editMode.value = false;
          } catch (error) {
            ElMessage.error(error.message || "更新失败");
          } finally {
            updating.value = false;
          }
        }
      });
    };

    // 显示修改密码对话框
    const showChangePasswordDialog = () => {
      passwordDialogVisible.value = true;
      passwordForm.oldPassword = "";
      passwordForm.newPassword = "";
      passwordForm.confirmPassword = "";
    };

    // 修改密码
    const changePassword = async () => {
      if (!passwordFormRef.value) return;

      await passwordFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            changingPassword.value = true;

            // 对密码进行SM3哈希
            const oldPasswordHash = SM3Hasher.hash(passwordForm.oldPassword);
            const newPasswordHash = SM3Hasher.hash(passwordForm.newPassword);

            await store.dispatch("user/changePassword", {
              oldPassword: oldPasswordHash,
              newPassword: newPasswordHash,
            });

            ElMessage.success("密码修改成功");
            passwordDialogVisible.value = false;
          } catch (error) {
            ElMessage.error(error.message || "密码修改失败");
          } finally {
            changingPassword.value = false;
          }
        }
      });
    };

    // 显示密钥管理对话框
    const showKeyManagementDialog = () => {
      keyDialogVisible.value = true;
      newKeyPair.publicKey = "";
      newKeyPair.privateKey = "";
    };

    // 生成新密钥对
    const generateNewKeyPair = () => {
      const keyPair = SM2Crypto.generateKeyPair();
      newKeyPair.publicKey = keyPair.publicKey;
      newKeyPair.privateKey = keyPair.privateKey;
    };

    // 复制文本到剪贴板的通用函数
    const copyToClipboard = (text, successMsg) => {
      // 检查navigator.clipboard API是否可用
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            ElMessage.success(successMsg);
          })
          .catch(() => {
            // 如果API调用失败，使用备用方法
            fallbackCopyToClipboard(text, successMsg);
          });
      } else {
        // 如果API不可用，使用备用方法
        fallbackCopyToClipboard(text, successMsg);
      }
    };

    // 备用复制方法（使用临时DOM元素）
    const fallbackCopyToClipboard = (text, successMsg) => {
      try {
        // 创建临时textarea元素
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 设置样式使其不可见
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);

        // 选择文本并复制
        textArea.focus();
        textArea.select();
        const successful = document.execCommand("copy");

        // 移除临时元素
        document.body.removeChild(textArea);

        if (successful) {
          ElMessage.success(successMsg);
        } else {
          ElMessage.error("复制失败，请手动复制");
        }
      } catch (err) {
        ElMessage.error("复制失败，请手动复制");
      }
    };

    // 下载文本文件的通用函数
    const downloadTextFile = (text, filename) => {
      const blob = new Blob([text], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      ElMessage.success(`${filename} 已下载`);
    };

    // 复制公钥
    const copyPublicKey = () => {
      copyToClipboard(newKeyPair.publicKey, "公钥已复制到剪贴板");
    };

    // 复制私钥
    const copyPrivateKey = () => {
      copyToClipboard(newKeyPair.privateKey, "私钥已复制到剪贴板");
    };

    // 下载公钥
    const downloadPublicKey = () => {
      downloadTextFile(newKeyPair.publicKey, "sm2_public_key.txt");
    };

    // 下载私钥
    const downloadPrivateKey = () => {
      downloadTextFile(newKeyPair.privateKey, "sm2_private_key.txt");
    };

    // 保存新公钥到服务器
    const saveNewPublicKey = async () => {
      try {
        await store.dispatch("user/updatePublicKey", {
          publicKey: newKeyPair.publicKey,
        });

        ElMessage.success("公钥更新成功");
        await getUserInfo();
      } catch (error) {
        ElMessage.error(error.message || "公钥更新失败");
      }
    };

    // 移除公钥
    const removePublicKey = async () => {
      try {
        await ElMessageBox.confirm(
          "移除公钥后将无法使用SM2证书登录，确定要继续吗？",
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        await store.dispatch("user/removePublicKey");
        ElMessage.success("公钥已移除");
        await getUserInfo();
      } catch (error) {
        if (error !== "cancel") {
          ElMessage.error(error.message || "移除公钥失败");
        }
      }
    };

    onMounted(() => {
      getUserInfo();
    });

    return {
      formRef,
      passwordFormRef,
      loading,
      updating,
      changingPassword,
      editMode,
      userInfo,
      form,
      rules,
      passwordDialogVisible,
      passwordForm,
      passwordRules,
      keyDialogVisible,
      newKeyPair,
      creditScoreColor,
      formatCreditScore,
      updateProfile,
      showChangePasswordDialog,
      changePassword,
      showKeyManagementDialog,
      generateNewKeyPair,
      copyPublicKey,
      copyPrivateKey,
      downloadPublicKey,
      downloadPrivateKey,
      saveNewPublicKey,
      removePublicKey,
      DocumentCopy,
      Download,
    };
  },
};
</script>

<style lang="scss" scoped>
.user-profile {
  max-width: 800px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
    }
  }
}

.loading-container {
  padding: 20px 0;
}

.form-hint {
  display: block;
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.security-options {
  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    .security-info {
      h4 {
        margin: 0 0 5px 0;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.key-management {
  .key-status {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
    }
  }

  .key-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }

  .new-key-pair {
    margin-top: 20px;

    h4 {
      margin: 0 0 10px 0;
    }

    .key-box {
      margin-bottom: 15px;

      .key-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        font-weight: bold;

        .key-actions-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }

    .key-warning {
      margin: 15px 0;
    }

    .save-key-action {
      margin-top: 20px;
      text-align: center;
    }
  }
}
</style>
