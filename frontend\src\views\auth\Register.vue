<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h2>基于国密算法的图书馆自习室座位管理系统</h2>
        <h3>用户注册</h3>
      </div>

      <el-steps :active="currentStep" finish-status="success" simple>
        <el-step title="填写信息" />
        <el-step title="生成密钥" />
        <el-step title="完成注册" />
      </el-steps>

      <!-- 步骤1：填写基本信息 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="top"
        >
          <el-form-item label="学号" prop="studentId">
            <el-input v-model="form.studentId" placeholder="请输入学号" />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>

          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="nextStep">下一步</el-button>
            <el-button @click="$router.push('/login')">返回登录</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤2：生成SM2密钥对 -->
      <div v-else-if="currentStep === 1" class="step-content">
        <div class="key-generation">
          <h3>SM2密钥对生成</h3>
          <p class="description">
            SM2密钥对用于安全登录和数据签名，请妥善保管您的私钥，公钥将上传至服务器。
          </p>

          <div class="key-box">
            <div class="key-label">
              <span>公钥</span>
              <div class="key-actions-buttons">
                <el-button type="primary" size="small" @click="copyPublicKey">
                  <el-icon><DocumentCopy /></el-icon> 复制
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="downloadPublicKey"
                >
                  <el-icon><Download /></el-icon> 下载
                </el-button>
              </div>
            </div>
            <el-input
              v-model="keyPair.publicKey"
              type="textarea"
              :rows="3"
              readonly
            />
          </div>

          <div class="key-box">
            <div class="key-label">
              <span>私钥</span>
              <div class="key-actions-buttons">
                <el-button type="primary" size="small" @click="copyPrivateKey">
                  <el-icon><DocumentCopy /></el-icon> 复制
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="downloadPrivateKey"
                >
                  <el-icon><Download /></el-icon> 下载
                </el-button>
              </div>
            </div>
            <el-input
              v-model="keyPair.privateKey"
              type="textarea"
              :rows="3"
              readonly
              show-password
            />
          </div>

          <div class="key-actions">
            <el-button type="primary" @click="regenerateKeyPair"
              >重新生成</el-button
            >
            <el-button type="success" @click="nextStep">下一步</el-button>
            <el-button @click="prevStep">上一步</el-button>
          </div>

          <el-alert
            title="重要提示"
            type="warning"
            description="请务必保存您的私钥，私钥将不会存储在服务器上，丢失后无法找回。"
            show-icon
            :closable="false"
            class="key-warning"
          />
        </div>
      </div>

      <!-- 步骤3：完成注册 -->
      <div v-else-if="currentStep === 2" class="step-content">
        <div class="register-confirm">
          <h3>确认注册信息</h3>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="学号">{{
              form.studentId
            }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{
              form.email
            }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{
              form.phone
            }}</el-descriptions-item>
            <el-descriptions-item label="是否使用SM2密钥"
              >是</el-descriptions-item
            >
          </el-descriptions>

          <div class="confirm-actions">
            <el-checkbox v-model="agreement">
              我已阅读并同意
              <a href="#" @click.prevent="showTerms">《用户协议》</a>
              和
              <a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
            </el-checkbox>

            <div class="button-group">
              <el-button
                type="primary"
                :loading="loading"
                :disabled="!agreement"
                @click="handleRegister"
              >
                提交注册
              </el-button>
              <el-button @click="prevStep">上一步</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤4：注册成功 -->
      <div v-else-if="currentStep === 3" class="step-content">
        <div class="register-success">
          <el-result
            icon="success"
            title="注册成功"
            sub-title="您已成功注册基于国密算法的图书馆自习室座位管理系统账号"
          >
            <template #extra>
              <el-button type="primary" @click="$router.push('/login')"
                >前往登录</el-button
              >
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from "vue";
import { useStore } from "vuex";

import { ElMessage, ElMessageBox } from "element-plus";
import { DocumentCopy, Download } from "@element-plus/icons-vue";
import { SM2Crypto, SM3Hasher } from "@/utils/crypto";

export default {
  name: "RegisterView",
  setup() {
    const store = useStore();

    const formRef = ref(null);
    const currentStep = ref(0);
    const loading = ref(false);
    const agreement = ref(false);

    // 注册表单
    const form = reactive({
      studentId: "",
      password: "",
      confirmPassword: "",
      email: "",
      phone: "",
    });

    // SM2密钥对
    const keyPair = reactive({
      publicKey: "",
      privateKey: "",
    });

    // 表单验证规则
    const validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (value.length < 6) {
        callback(new Error("密码长度不能小于6位"));
      } else {
        if (form.confirmPassword !== "") {
          formRef.value.validateField("confirmPassword");
        }
        callback();
      }
    };

    const validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== form.password) {
        callback(new Error("两次输入密码不一致"));
      } else {
        callback();
      }
    };

    const rules = {
      studentId: [
        { required: true, message: "请输入学号", trigger: "blur" },
        { pattern: /^\d{11}$/, message: "学号必须为11位数字", trigger: "blur" },
      ],
      password: [{ validator: validatePass, trigger: "blur" }],
      confirmPassword: [{ validator: validatePass2, trigger: "blur" }],
      email: [
        { required: true, message: "请输入邮箱", trigger: "blur" },
        { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
      ],
      phone: [
        { required: true, message: "请输入手机号", trigger: "blur" },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: "请输入正确的手机号格式",
          trigger: "blur",
        },
      ],
    };

    // 生成SM2密钥对
    const generateKeyPair = () => {
      const newKeyPair = SM2Crypto.generateKeyPair();
      keyPair.publicKey = newKeyPair.publicKey;
      keyPair.privateKey = newKeyPair.privateKey;
    };

    // 重新生成SM2密钥对
    const regenerateKeyPair = () => {
      ElMessageBox.confirm(
        "重新生成密钥对将覆盖当前密钥，确定要继续吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          generateKeyPair();
          ElMessage.success("密钥对已重新生成");
        })
        .catch(() => {
          // 用户取消操作
        });
    };

    // 复制文本到剪贴板的通用函数
    const copyToClipboard = (text, successMsg) => {
      // 检查navigator.clipboard API是否可用
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            ElMessage.success(successMsg);
          })
          .catch(() => {
            // 如果API调用失败，使用备用方法
            fallbackCopyToClipboard(text, successMsg);
          });
      } else {
        // 如果API不可用，使用备用方法
        fallbackCopyToClipboard(text, successMsg);
      }
    };

    // 备用复制方法（使用临时DOM元素）
    const fallbackCopyToClipboard = (text, successMsg) => {
      try {
        // 创建临时textarea元素
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 设置样式使其不可见
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);

        // 选择文本并复制
        textArea.focus();
        textArea.select();
        const successful = document.execCommand("copy");

        // 移除临时元素
        document.body.removeChild(textArea);

        if (successful) {
          ElMessage.success(successMsg);
        } else {
          ElMessage.error("复制失败，请手动复制");
        }
      } catch (err) {
        ElMessage.error("复制失败，请手动复制");
      }
    };

    // 下载文本文件的通用函数
    const downloadTextFile = (text, filename) => {
      const blob = new Blob([text], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      ElMessage.success(`${filename} 已下载`);
    };

    // 复制公钥
    const copyPublicKey = () => {
      copyToClipboard(keyPair.publicKey, "公钥已复制到剪贴板");
    };

    // 复制私钥
    const copyPrivateKey = () => {
      copyToClipboard(keyPair.privateKey, "私钥已复制到剪贴板");
    };

    // 下载公钥
    const downloadPublicKey = () => {
      downloadTextFile(keyPair.publicKey, "sm2_public_key.txt");
    };

    // 下载私钥
    const downloadPrivateKey = () => {
      downloadTextFile(keyPair.privateKey, "sm2_private_key.txt");
    };

    // 下一步
    const nextStep = async () => {
      if (currentStep.value === 0) {
        if (!formRef.value) return;

        await formRef.value.validate(async (valid) => {
          if (valid) {
            currentStep.value++;

            // 如果还没有生成密钥对，则生成
            if (!keyPair.publicKey || !keyPair.privateKey) {
              generateKeyPair();
            }
          }
        });
      } else {
        currentStep.value++;
      }
    };

    // 上一步
    const prevStep = () => {
      currentStep.value--;
    };

    // 显示用户协议
    const showTerms = () => {
      ElMessageBox.alert("这里是用户协议内容...", "用户协议", {
        confirmButtonText: "确定",
      });
    };

    // 显示隐私政策
    const showPrivacy = () => {
      ElMessageBox.alert("这里是隐私政策内容...", "隐私政策", {
        confirmButtonText: "确定",
      });
    };

    // 提交注册
    const handleRegister = async () => {
      try {
        loading.value = true;

        // 对密码进行SM3哈希
        const hashedPassword = SM3Hasher.hash(form.password);

        // 调用注册接口
        await store.dispatch("user/register", {
          studentId: form.studentId,
          password: hashedPassword,
          email: form.email,
          phone: form.phone,
          publicKey: keyPair.publicKey,
        });

        // 注册成功，进入下一步
        currentStep.value = 3;
        ElMessage.success("注册成功");
      } catch (error) {
        ElMessage.error(error.message || "注册失败，请稍后重试");
      } finally {
        loading.value = false;
      }
    };

    return {
      formRef,
      currentStep,
      loading,
      agreement,
      form,
      keyPair,
      rules,
      nextStep,
      prevStep,
      generateKeyPair,
      regenerateKeyPair,
      copyPublicKey,
      copyPrivateKey,
      downloadPublicKey,
      downloadPrivateKey,
      showTerms,
      showPrivacy,
      handleRegister,
      DocumentCopy,
      Download,
    };
  },
};
</script>

<style lang="scss" scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-image: url("@/assets/background.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  padding: 20px;
}

/* 移除半透明白色遮罩层 */

.register-card {
  width: 500px;
  padding: 30px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    font-size: 22px;
    color: #303133;
    margin: 0 0 10px 0;
    font-weight: 600;
    line-height: 1.4;
  }

  h3 {
    font-size: 18px;
    color: #606266;
    margin: 0;
  }

  /* 删除logo相关样式 */
}

.step-content {
  margin-top: 30px;
}

.key-generation {
  .description {
    margin-bottom: 20px;
    color: #606266;
  }

  .key-box {
    margin-bottom: 20px;

    .key-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      font-weight: bold;

      .key-actions-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  .key-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }

  .key-warning {
    margin-top: 20px;
  }
}

.register-confirm {
  .confirm-actions {
    margin-top: 30px;

    .button-group {
      margin-top: 20px;
      display: flex;
      gap: 10px;
    }
  }
}

.register-success {
  padding: 20px 0;
}
</style>
