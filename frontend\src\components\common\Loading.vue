<template>
  <div class="loading-container" :class="{ fullscreen }">
    <el-loading
      v-if="visible"
      :fullscreen="fullscreen"
      :text="text"
      :background="background"
    ></el-loading>
  </div>
</template>

<script>
export default {
  name: "LoadingComponent",
  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
      default: "加载中...",
    },
    background: {
      type: String,
      default: "rgba(255, 255, 255, 0.8)",
    },
  },
};
</script>

<style lang="scss" scoped>
.loading-container {
  position: relative;
  min-height: 100px;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
}
</style>
