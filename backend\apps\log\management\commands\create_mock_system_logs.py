from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import connection
from apps.log.models import SystemLog, SecurityLog
from apps.admin_management.models import Admin
from utils.crypto import SM3Hasher
import random
import datetime
import ipaddress
import json
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '创建模拟系统日志和安全日志数据，包括一些篡改的日志记录'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=30, help='要创建的每种日志数量')
        parser.add_argument('--days', type=int, default=7, help='日志分布的天数')
        parser.add_argument('--tamper', action='store_true', help='是否篡改部分日志')

    def handle(self, *args, **options):
        count = options['count']
        days = options['days']
        tamper = options['tamper']
        
        # 获取所有管理员
        admins = list(Admin.objects.all())
        
        self.stdout.write(f'开始创建 {count} 条模拟系统日志和安全日志...')
        
        # 系统日志类型列表
        log_types = ['info', 'warning', 'error', 'security']
        
        # 模块名称列表
        modules = [
            'authentication', 'admin_management', 'seat', 'reservation',
            'blacklist', 'notification', 'statistics', 'system'
        ]
        
        # 操作名称列表
        actions = [
            'create', 'update', 'delete', 'query', 'login', 'logout',
            'register', 'reset_password', 'export', 'import', 'backup',
            'restore', 'schedule', 'notify', 'verify', 'check'
        ]
        
        # 安全日志类型列表
        security_log_types = [
            'login_attempt', 'auth_failure', 'token_issue', 'token_revoke',
            'password_change', 'permission_change', 'config_change',
            'admin_operation', 'suspicious_activity'
        ]
        
        # 生成随机IP地址
        def random_ip():
            return str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))
        
        # 计算日期范围
        end_date = timezone.now()
        start_date = end_date - datetime.timedelta(days=days)
        
        # 创建系统日志
        system_logs_created = 0
        for i in range(count):
            try:
                # 随机选择日志类型
                log_type = random.choice(log_types)
                
                # 随机选择模块
                module = random.choice(modules)
                
                # 随机选择操作
                action = random.choice(actions)
                
                # 随机选择用户类型和ID
                user_type = 'admin' if random.random() < 0.6 else 'user'
                user_id = random.choice(admins).id if user_type == 'admin' and admins else random.randint(1, 100)
                
                # 生成描述
                description = f'{module}.{action}: '
                if log_type == 'info':
                    description += f'操作成功完成'
                elif log_type == 'warning':
                    description += f'操作完成但有警告'
                elif log_type == 'error':
                    description += f'操作失败，错误代码: {random.randint(1000, 9999)}'
                elif log_type == 'security':
                    description += f'安全相关操作，状态: {"成功" if random.random() < 0.7 else "失败"}'
                
                # 随机生成创建时间
                random_seconds = random.randint(0, int((end_date - start_date).total_seconds()))
                created_at = start_date + datetime.timedelta(seconds=random_seconds)
                
                # 获取前一条日志的哈希值
                prev_log = SystemLog.objects.order_by('-id').first()
                prev_hash = prev_log.hash_value if prev_log else None
                
                # 准备日志数据用于哈希计算
                log_data = {
                    'log_type': log_type,
                    'module': module,
                    'action': action,
                    'description': description,
                    'user_id': user_id,
                    'user_type': user_type,
                    'ip_address': random_ip(),
                    'created_at': created_at.isoformat(),
                }
                
                # 计算哈希值
                hash_value = SM3Hasher.hash(json.dumps(log_data, sort_keys=True) + (prev_hash or ""))
                
                # 创建日志记录
                log = SystemLog.objects.create(
                    log_type=log_type,
                    module=module,
                    action=action,
                    description=description,
                    user_id=user_id,
                    user_type=user_type,
                    ip_address=log_data['ip_address'],
                    created_at=created_at,
                    hash_value=hash_value,
                    prev_hash=prev_hash
                )
                
                system_logs_created += 1
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'创建系统日志时出错: {str(e)}'))
        
        # 创建安全日志
        security_logs_created = 0
        for i in range(count):
            try:
                # 随机选择日志类型
                log_type = random.choice(security_log_types)
                
                # 随机选择用户类型和ID
                user_type = 'admin' if random.random() < 0.6 else 'user'
                user_id = random.choice(admins).id if user_type == 'admin' and admins else random.randint(1, 100)
                
                # 随机选择状态
                status = 'success' if random.random() < 0.7 else 'failed'
                
                # 生成描述
                description = f'安全事件: {log_type}, '
                if log_type == 'login_attempt':
                    description += f'用户尝试登录，状态: {status}'
                elif log_type == 'auth_failure':
                    description += f'认证失败，原因: {"密码错误" if random.random() < 0.7 else "账户锁定"}'
                elif log_type == 'token_issue':
                    description += f'颁发新令牌，有效期: {random.randint(1, 24)}小时'
                elif log_type == 'token_revoke':
                    description += f'撤销令牌，原因: {"用户登出" if random.random() < 0.7 else "安全策略"}'
                elif log_type == 'password_change':
                    description += f'密码已更改，状态: {status}'
                else:
                    description += f'状态: {status}'
                
                # 随机生成创建时间
                random_seconds = random.randint(0, int((end_date - start_date).total_seconds()))
                created_at = start_date + datetime.timedelta(seconds=random_seconds)
                
                # 获取前一条日志的哈希值
                prev_log = SecurityLog.objects.order_by('-id').first()
                prev_hash = prev_log.hash_value if prev_log else None
                
                # 准备日志数据用于哈希计算
                log_data = {
                    'log_type': log_type,
                    'description': description,
                    'ip_address': random_ip(),
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'user_id': user_id,
                    'user_type': user_type,
                    'status': status,
                    'created_at': created_at.isoformat(),
                }
                
                # 计算哈希值
                hash_value = SM3Hasher.hash(json.dumps(log_data, sort_keys=True) + (prev_hash or ""))
                
                # 创建日志记录
                log = SecurityLog.objects.create(
                    log_type=log_type,
                    description=description,
                    ip_address=log_data['ip_address'],
                    user_agent=log_data['user_agent'],
                    user_id=user_id,
                    user_type=user_type,
                    status=status,
                    created_at=created_at,
                    hash_value=hash_value,
                    prev_hash=prev_hash
                )
                
                security_logs_created += 1
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'创建安全日志时出错: {str(e)}'))
        
        # 篡改部分日志记录
        if tamper:
            self.stdout.write('开始篡改部分日志记录...')
            
            # 篡改系统日志
            if SystemLog.objects.count() > 5:
                # 随机选择一条系统日志进行篡改
                log_id = SystemLog.objects.order_by('?').first().id
                
                # 直接使用SQL更新，绕过ORM以避免触发任何钩子
                with connection.cursor() as cursor:
                    cursor.execute(
                        "UPDATE system_log SET description = %s WHERE id = %s",
                        ["这条日志已被篡改，用于测试日志审计功能", log_id]
                    )
                
                self.stdout.write(f'已篡改系统日志ID: {log_id}')
            
            # 篡改安全日志
            if SecurityLog.objects.count() > 5:
                # 随机选择一条安全日志进行篡改
                log_id = SecurityLog.objects.order_by('?').first().id
                
                # 直接使用SQL更新，绕过ORM以避免触发任何钩子
                with connection.cursor() as cursor:
                    cursor.execute(
                        "UPDATE security_log SET description = %s, status = %s WHERE id = %s",
                        ["这条日志已被篡改，用于测试日志审计功能", "success", log_id]
                    )
                
                self.stdout.write(f'已篡改安全日志ID: {log_id}')
        
        self.stdout.write(self.style.SUCCESS(f'成功创建 {system_logs_created} 条系统日志和 {security_logs_created} 条安全日志'))
