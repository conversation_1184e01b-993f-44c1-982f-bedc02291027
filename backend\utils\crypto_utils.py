"""
混合加密方案工具类
提供前后端混合加密通信功能
"""
import os
import base64
import json
import logging
import binascii
from django.conf import settings
from utils.crypto import SM2Crypto, SM3Hasher, SM4Crypto
from utils.key_manager import KeyManager

logger = logging.getLogger(__name__)

class CryptoUtils:
    """加密工具类"""
    
    @staticmethod
    def generate_sm2_keypair():
        """生成SM2密钥对"""
        return SM2Crypto.generate_key_pair()
    
    @staticmethod
    def generate_sm4_key():
        """生成随机SM4密钥"""
        return SM4Crypto.generate_key()
    
    @staticmethod
    def get_server_public_key():
        """
        获取服务器SM2公钥
        
        返回:
            公钥(十六进制字符串)
        """
        try:
            # 从配置文件读取系统SM2公钥
            with open(settings.SM2_PUBLIC_KEY_PATH, 'rb') as f:
                public_key = f.read()
            
            # 如果是字节，转换为十六进制字符串
            if isinstance(public_key, bytes):
                public_key = public_key.hex()
            
            return public_key
        except Exception as e:
            logger.error(f"获取服务器公钥失败: {str(e)}")
            raise RuntimeError("获取服务器公钥失败") from e
    
    @staticmethod
    def encrypt_for_transmission(data):
        """
        使用混合加密方案加密数据用于传输
        
        参数:
            data: 待加密数据(字典或字符串)
            
        返回:
            {
                'encrypted_data': SM4加密的数据(十六进制字符串),
                'encrypted_key': SM2加密的SM4密钥(十六进制字符串),
                'iv': 初始向量(十六进制字符串)
            }
        """
        try:
            # 如果是字典，转换为JSON字符串
            if isinstance(data, dict):
                data = json.dumps(data)
            
            # 生成随机SM4密钥
            sm4_key = SM4Crypto.generate_key()
            
            # 使用SM4加密数据，获取分离的密文和IV
            encryption_result = SM4Crypto.encrypt_for_frontend(sm4_key, data)
            ciphertext = encryption_result['ciphertext']
            iv = encryption_result['iv']
            
            # 获取服务器公钥
            server_public_key = CryptoUtils.get_server_public_key()
            
            # 使用SM2加密SM4密钥
            encrypted_key = SM2Crypto.encrypt_for_frontend(server_public_key, sm4_key)
            
            return {
                'encrypted_data': ciphertext,
                'encrypted_key': encrypted_key,
                'iv': iv
            }
        except Exception as e:
            logger.error(f"混合加密失败: {str(e)}")
            raise RuntimeError("混合加密失败") from e
    
    @staticmethod
    def decrypt_from_transmission(encrypted_package):
        """
        解密使用混合加密方案传输的数据
        
        参数:
            encrypted_package: 加密包
                {
                    'encrypted_data': SM4加密的数据(十六进制字符串),
                    'encrypted_key': SM2加密的SM4密钥(十六进制字符串),
                    'iv': 初始向量(十六进制字符串)
                }
                
        返回:
            解密后的数据(字符串)
        """
        try:
            # 提取加密数据
            encrypted_data = encrypted_package.get('encrypted_data')
            encrypted_key = encrypted_package.get('encrypted_key')
            iv = encrypted_package.get('iv')
            
            if not all([encrypted_data, encrypted_key, iv]):
                raise ValueError("加密包缺少必要字段")
            
            # 从配置文件读取系统SM2私钥
            with open(settings.SM2_PRIVATE_KEY_PATH, 'rb') as f:
                private_key = f.read().decode().strip()
            
            # 使用SM2解密SM4密钥
            sm4_key = SM2Crypto.decrypt_from_frontend(private_key, encrypted_key)
            
            # 使用SM4解密数据
            decrypted_data = SM4Crypto.decrypt_from_frontend(sm4_key, encrypted_data, iv)
            
            # 尝试解析JSON
            try:
                return json.loads(decrypted_data)
            except json.JSONDecodeError:
                return decrypted_data
        except Exception as e:
            logger.error(f"混合解密失败: {str(e)}")
            raise RuntimeError("混合解密失败") from e
    
    @staticmethod
    def hash_password(password, salt=None, iterations=10000):
        """
        使用SM3哈希算法和盐值对密码进行哈希处理
        
        参数:
            password: 密码
            salt: 盐值，如果为None则随机生成
            iterations: 迭代次数
            
        返回:
            {
                'hash': 哈希值(十六进制字符串),
                'salt': 盐值(十六进制字符串),
                'iterations': 迭代次数
            }
        """
        return SM3Hasher.hash_with_salt(password, salt, iterations)
    
    @staticmethod
    def verify_password(password, hash_value, salt, iterations=10000):
        """
        验证密码是否匹配哈希值
        
        参数:
            password: 密码
            hash_value: 哈希值(十六进制字符串)
            salt: 盐值(十六进制字符串)
            iterations: 迭代次数
            
        返回:
            是否匹配(布尔值)
        """
        return SM3Hasher.verify(password, hash_value, salt, iterations)
    
    @staticmethod
    def sign_data(data, private_key):
        """
        使用SM2签名数据
        
        参数:
            data: 待签名数据(字典或字符串)
            private_key: SM2私钥(十六进制字符串)
            
        返回:
            签名(十六进制字符串)
        """
        # 如果是字典，转换为JSON字符串
        if isinstance(data, dict):
            data = json.dumps(data, sort_keys=True)
        
        return SM2Crypto.sign(private_key, data)
    
    @staticmethod
    def verify_signature(data, signature, public_key):
        """
        验证SM2签名
        
        参数:
            data: 原始数据(字典或字符串)
            signature: 签名(十六进制字符串)
            public_key: SM2公钥(十六进制字符串)
            
        返回:
            验证结果(布尔值)
        """
        # 如果是字典，转换为JSON字符串
        if isinstance(data, dict):
            data = json.dumps(data, sort_keys=True)
        
        return SM2Crypto.verify(public_key, data, signature)
