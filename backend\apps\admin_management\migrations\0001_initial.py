# Generated by Django 4.2.7 on 2025-05-21 05:17

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Admin',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('username', models.CharField(max_length=50, unique=True, verbose_name='用户名')),
                ('password', models.Char<PERSON>ield(max_length=64, verbose_name='SM3哈希的密码')),
                ('salt', models.CharField(blank=True, max_length=32, null=True, verbose_name='密码盐值')),
                ('iterations', models.IntegerField(default=10000, verbose_name='密码哈希迭代次数')),
                ('role', models.CharField(default='admin', max_length=20, verbose_name='角色(admin/super_admin)')),
                ('auth_key', models.Char<PERSON>ield(blank=True, max_length=64, null=True, verbose_name='TOTP认证密钥(SM4加密)')),
                ('email', models.BinaryField(blank=True, max_length=128, null=True, verbose_name='SM4加密的邮箱')),
                ('phone', models.BinaryField(blank=True, max_length=64, null=True, verbose_name='SM4加密的手机号')),
                ('status', models.CharField(default='active', max_length=20, verbose_name='状态(active/disabled)')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '管理员',
                'verbose_name_plural': '管理员',
                'db_table': 'admin',
            },
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('key', models.CharField(max_length=50, unique=True, verbose_name='配置键')),
                ('value', models.TextField(verbose_name='配置值')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='配置描述')),
                ('is_encrypted', models.BooleanField(default=False, verbose_name='是否加密')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='admin_management.admin', verbose_name='更新人')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'db_table': 'system_config',
            },
        ),
        migrations.CreateModel(
            name='AdminSession',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('token', models.CharField(max_length=64, verbose_name='JWT令牌ID')),
                ('ip_address', models.CharField(max_length=45, verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('admin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_management.admin', verbose_name='管理员ID')),
            ],
            options={
                'verbose_name': '管理员会话',
                'verbose_name_plural': '管理员会话',
                'db_table': 'admin_session',
                'indexes': [models.Index(fields=['admin'], name='admin_sessi_admin_i_c6bec0_idx'), models.Index(fields=['token'], name='admin_sessi_token_55f74d_idx'), models.Index(fields=['expires_at'], name='admin_sessi_expires_803cb9_idx')],
            },
        ),
        migrations.CreateModel(
            name='AdminOperationLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='主键ID')),
                ('operation', models.CharField(max_length=50, verbose_name='操作类型')),
                ('description', models.TextField(verbose_name='操作描述')),
                ('ip_address', models.CharField(max_length=45, verbose_name='IP地址')),
                ('target_type', models.CharField(blank=True, max_length=20, null=True, verbose_name='目标类型')),
                ('target_id', models.BigIntegerField(blank=True, null=True, verbose_name='目标ID')),
                ('status', models.CharField(default='success', max_length=20, verbose_name='状态(success/failed)')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('admin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_management.admin', verbose_name='管理员ID')),
            ],
            options={
                'verbose_name': '管理员操作日志',
                'verbose_name_plural': '管理员操作日志',
                'db_table': 'admin_operation_log',
                'indexes': [models.Index(fields=['admin'], name='admin_opera_admin_i_6b2417_idx'), models.Index(fields=['operation'], name='admin_opera_operati_d53812_idx'), models.Index(fields=['created_at'], name='admin_opera_created_3f4300_idx')],
            },
        ),
    ]
